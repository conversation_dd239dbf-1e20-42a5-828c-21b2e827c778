/**
 * 超时检测暂停/恢复功能测试
 * 验证用户离开/返回时的计时器控制
 */

import { useTouchlessActionTimeout } from '@/composables/useTouchlessActionTimeout'

export function runTimeoutPauseResumeTest() {
  console.log('🧪 开始超时检测暂停/恢复测试...')
  
  const timeout = useTouchlessActionTimeout()
  
  // 模拟动作配置
  const mockAction = {
    action_id: 'test_action_001',
    action_name: '测试动作',
    action_type: 'shoulder_touch',
    side: 'left'
  }
  
  // 模拟回调函数
  const mockCallbacks = {
    onTimeout: (data) => {
      console.log('⏰ 超时回调触发:', data)
    },
    onWarning: (data) => {
      console.log('⚠️ 警告回调触发:', data)
    }
  }
  
  console.log('📋 测试步骤:')
  console.log('1. 启动超时检测')
  console.log('2. 等待5秒后暂停')
  console.log('3. 等待5秒后恢复')
  console.log('4. 观察计时器行为')
  
  // 步骤1: 启动超时检测
  console.log('\n🚀 步骤1: 启动超时检测')
  timeout.startDetection(mockAction, mockCallbacks)
  
  let initialStatus = timeout.getDetectionStatus()
  console.log('初始状态:', initialStatus)
  
  // 步骤2: 5秒后暂停
  setTimeout(() => {
    console.log('\n⏸️ 步骤2: 暂停超时检测')
    let beforePauseStatus = timeout.getDetectionStatus()
    console.log('暂停前状态:', beforePauseStatus)
    
    timeout.pauseDetection()
    
    let afterPauseStatus = timeout.getDetectionStatus()
    console.log('暂停后状态:', afterPauseStatus)
    
    if (afterPauseStatus.isPaused) {
      console.log('✅ 暂停状态正确')
    } else {
      console.log('❌ 暂停状态错误')
    }
  }, 5000)
  
  // 步骤3: 10秒后恢复
  setTimeout(() => {
    console.log('\n▶️ 步骤3: 恢复超时检测')
    let beforeResumeStatus = timeout.getDetectionStatus()
    console.log('恢复前状态:', beforeResumeStatus)
    
    timeout.resumeDetection()
    
    let afterResumeStatus = timeout.getDetectionStatus()
    console.log('恢复后状态:', afterResumeStatus)
    
    if (!afterResumeStatus.isPaused && afterResumeStatus.isActive) {
      console.log('✅ 恢复状态正确')
    } else {
      console.log('❌ 恢复状态错误')
    }
  }, 10000)
  
  // 步骤4: 15秒后检查最终状态
  setTimeout(() => {
    console.log('\n📊 步骤4: 最终状态检查')
    let finalStatus = timeout.getDetectionStatus()
    console.log('最终状态:', finalStatus)
    
    // 停止检测
    timeout.stopDetection()
    console.log('✅ 测试完成，已停止检测')
    
    console.log('\n🎯 测试总结:')
    console.log('- 检查控制台是否在暂停期间停止了"检查进度"日志')
    console.log('- 检查恢复后是否正确重新开始计时')
    console.log('- 检查暂停时长是否正确计算')
  }, 15000)
  
  console.log('\n⏱️ 测试运行中，请观察控制台输出...')
}

// 导出测试函数供开发环境使用
if (import.meta.env.DEV) {
  window.runTimeoutPauseResumeTest = runTimeoutPauseResumeTest
  console.log('🔧 开发模式：可通过 window.runTimeoutPauseResumeTest() 运行暂停/恢复测试')
}
