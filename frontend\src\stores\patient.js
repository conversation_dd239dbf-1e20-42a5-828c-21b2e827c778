/**
 * 患者信息和校验管理
 * 负责患者识别、校验和用户信息管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const usePatientStore = defineStore('patient', () => {
  // 用户信息
  const userInfo = ref(null)
  
  // 患者校验相关状态
  const patientValidation = ref({
    timer: null,
    isValidating: false,
    validationCount: 0,
    lastDetectedId: null,
    consecutiveCount: 0,
    requiredConsecutiveCount: 3, // 需要连续检测到3次相同ID
    // 新增：检测状态
    detectionStatus: 'waiting', // 'waiting' | 'no_user' | 'unknown_user' | 'detecting_user' | 'validating'
    currentDetectedId: null // 当前检测到的ID
  })

  // 计算属性
  const isUserLoggedIn = computed(() => {
    return userInfo.value && userInfo.value.patient_id
  })
  /**
   * 启动patientId校验
   * 在waiting状态时调用，每2秒检查一次patientId
   */
  const startPatientValidation = (getCurrentPatientId) => {
    // 如果已经在校验中，先停止之前的校验
    if (patientValidation.value.timer) {
      stopPatientValidation()
    }
    
    console.log('[PatientValidation] 开始patientId校验')
    patientValidation.value.isValidating = true
    patientValidation.value.validationCount = 0
    patientValidation.value.lastDetectedId = null
    patientValidation.value.consecutiveCount = 0
    
    patientValidation.value.timer = setInterval(() => {
      validatePatientId(getCurrentPatientId())
    }, 2000)
  }

  /**
   * 停止patientId校验
   * 清理定时器和重置校验状态
   */
  const stopPatientValidation = () => {
    console.log('[PatientValidation] 停止patientId校验')
    // 清理定时器
    if (patientValidation.value.timer) {
      clearInterval(patientValidation.value.timer)
      patientValidation.value.timer = null
    }
    // 重置校验状态
    patientValidation.value.isValidating = false
    patientValidation.value.validationCount = 0
    patientValidation.value.lastDetectedId = null
    patientValidation.value.consecutiveCount = 0
    patientValidation.value.detectionStatus = 'waiting'
    patientValidation.value.currentDetectedId = null
  }

  /**
   * 校验patientId
   * 核心校验逻辑：连续3次检测到相同有效ID则认为校验成功
   */
  const validatePatientId = (currentPatientId) => {
    // 增加校验计数
    patientValidation.value.validationCount++

    console.log(`[PatientValidation] 第${patientValidation.value.validationCount}次校验，当前ID: ${currentPatientId}`)

    // 更新当前检测到的ID
    patientValidation.value.currentDetectedId = currentPatientId

    // 检查是否有有效的patientId
    if (!currentPatientId || currentPatientId.trim() === '') {
      console.log('[PatientValidation] 未检测到用户')
      patientValidation.value.consecutiveCount = 0
      patientValidation.value.lastDetectedId = null
      patientValidation.value.detectionStatus = 'no_user'
      return false
    }

    if (currentPatientId === 'unknown') {
      console.log('[PatientValidation] 检测到未授权用户')
      patientValidation.value.consecutiveCount = 0
      patientValidation.value.lastDetectedId = null
      patientValidation.value.detectionStatus = 'unknown_user'
      return false
    }

    // 检查是否与上次检测到的ID相同
    if (patientValidation.value.lastDetectedId === currentPatientId) {
      // 相同ID，增加连续计数
      patientValidation.value.consecutiveCount++
      console.log(`[PatientValidation] 连续第${patientValidation.value.consecutiveCount}次检测到相同ID: ${currentPatientId}`)

      // 更新状态为正在验证
      patientValidation.value.detectionStatus = 'validating'

      // 检查是否达到所需的连续次数
      if (patientValidation.value.consecutiveCount >= patientValidation.value.requiredConsecutiveCount) {
        console.log(`[PatientValidation] 校验成功! 连续${patientValidation.value.consecutiveCount}次检测到ID: ${currentPatientId}`)

        // 校验成功，停止校验并处理成功逻辑
        stopPatientValidation()
        handleValidationSuccess(currentPatientId)
        return true
      }
    } else {
      // 不同ID，重置连续计数并更新lastDetectedId
      console.log(`[PatientValidation] 检测到新ID: ${currentPatientId}，重置连续计数`)
      patientValidation.value.consecutiveCount = 1
      patientValidation.value.lastDetectedId = currentPatientId
      patientValidation.value.detectionStatus = 'detecting_user'
    }
    
    return false
  }

  /**
   * 处理校验成功逻辑
   * 校验成功后直接更新用户信息
   */
  const handleValidationSuccess = (validatedPatientId) => {
    // 检查传入的ID是否有效
    if (!validatedPatientId || validatedPatientId === 'unknown') {
      return
    }
    // 构造用户信息
    const newUserInfo = {
      patient_id: validatedPatientId,
      patient_name: validatedPatientId,
      age: 22,
      gender: "male",
      login_time: new Date().toISOString(),
      validation_method: "frontend_auto",
    };
    console.log(`[PatientValidation] 🔍 即将设置用户信息:`, newUserInfo)
    userInfo.value = newUserInfo;
    console.log(`[PatientValidation] ✅ 用户信息设置完成，当前值:`, userInfo.value)
  }

  /**
   * 重置患者数据
   */
  const resetPatientData = () => {
    userInfo.value = null
    stopPatientValidation()
  }

  return {
    // 响应式数据
    userInfo,
    patientValidation,

    // 计算属性
    isUserLoggedIn,

    // 方法
    startPatientValidation,
    stopPatientValidation,
    validatePatientId,
    handleValidationSuccess,
    resetPatientData,
    
  }
})
