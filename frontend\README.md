# 智能康复训练系统 (Frontend)

<div align="center">

![Vue.js](https://img.shields.io/badge/Vue.js-3.4.0-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white)
![Vite](https://img.shields.io/badge/Vite-5.0.8-646CFF?style=for-the-badge&logo=vite&logoColor=white)
![Element Plus](https://img.shields.io/badge/Element_Plus-2.4.4-409EFF?style=for-the-badge&logo=element&logoColor=white)
![TailwindCSS](https://img.shields.io/badge/Tailwind_CSS-3.4.0-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)

**基于AI姿态检测的智能康复训练平台前端应用**

[功能特性](#功能特性) • [技术架构](#技术架构) • [快速开始](#快速开始) • [开发指南](#开发指南)

</div>

---

## 📋 项目概览

智能康复训练系统是一个基于计算机视觉和人工智能技术的现代化康复训练平台。本项目为系统的前端应用，采用Vue 3生态系统构建，提供直观的用户界面和流畅的交互体验。

### 🎯 核心价值

- **🔬 智能评估**: 基于COCO-WholeBody 133关键点的精准姿态分析
- **👤 身份识别**: 自动患者识别和身份验证系统
- **📊 实时反馈**: 即时动作评估和视觉反馈
- **🎵 多感官体验**: 音频提示和庆祝动画增强用户体验
- **📈 数据驱动**: 完整的训练数据记录和报告生成
- **🚫 无接触控制**: 智能超时检测和异常处理机制

### 🌟 技术亮点

- **现代化架构**: Vue 3 Composition API + Vite + TypeScript
- **模块化设计**: Pinia状态管理的模块化架构
- **实时通信**: WebSocket + Socket.IO实现低延迟数据传输
- **响应式UI**: Element Plus + TailwindCSS构建的现代化界面
- **状态机驱动**: 基于有限状态机的工作流管理
- **组合式函数**: 高度可复用的业务逻辑封装

---

## ✨ 功能特性

### 🔐 用户认证与身份管理
- **智能人脸识别**: 基于摄像头的自动患者识别
- **身份验证**: 多重验证机制确保训练安全性
- **用户状态管理**: 实时监控用户在线状态和身份变更

### 🏃‍♂️ 康复训练核心功能
- **多动作支持**:
  - 对侧触肩 (Shoulder Touch)
  - 手臂上举 (Arm Raise)
  - 指尖对触 (Finger Touch) *开发中*
  - 手掌翻转 (Palm Flip) *开发中*
- **实时评估**: 基于姿态关键点的动作质量评分 (0-100分)
- **智能引导**: 动作演示视频和实时指导提示
- **进度跟踪**: 训练进度可视化和历史记录

### 📺 视觉反馈系统
- **实时视频流**: 高质量摄像头画面显示
- **姿态可视化**: 关键点和骨架线实时渲染
- **人脸检测框**: 实时人脸位置和ID显示
- **动作轨迹**: 运动路径和目标区域可视化

### 🎵 多媒体反馈
- **音频提示**: 动作指导和完成提示音效
- **庆祝动画**: 动作完成后的视觉奖励效果
- **进度音效**: 训练进度变化的声音反馈

### 🚨 智能异常处理
- **超时检测**: 90秒智能超时，60秒警告提示
- **身份监控**: 实时检测用户离开或身份变更
- **自动恢复**: 异常情况下的自动暂停和恢复机制
- **紧急暂停**: 多种异常情况的紧急处理

### 📊 数据管理与报告
- **训练报告**: 详细的训练数据分析和可视化
- **数据导出**: JSON格式的训练数据导出功能
- **本地存储**: 训练历史的本地缓存和管理
- **性能统计**: 动作完成率、平均分数等统计信息

---

## 🏗️ 技术架构

### 📦 技术栈

| 类别 | 技术 | 版本 | 用途 |
|------|------|------|------|
| **框架** | Vue.js | 3.4.0 | 核心前端框架 |
| **构建工具** | Vite | 5.0.8 | 开发服务器和构建工具 |
| **状态管理** | Pinia | 2.1.7 | 响应式状态管理 |
| **路由** | Vue Router | 4.2.5 | 单页应用路由 |
| **UI组件** | Element Plus | 2.4.4 | 企业级UI组件库 |
| **样式框架** | TailwindCSS | 3.4.0 | 原子化CSS框架 |
| **实时通信** | Socket.IO Client | 4.7.4 | WebSocket通信 |

### 🏛️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    智能康复训练系统架构                        │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Vue 3)                                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Views     │ │ Components  │ │ Composables │           │
│  │ (页面组件)   │ │ (UI组件)    │ │ (业务逻辑)   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Pinia Store (状态管理)                     │ │
│  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │ │
│  │ │Workflow │ │Training │ │Patient  │ │Connection│       │ │
│  │ └─────────┘ └─────────┘ └─────────┘ └─────────┘       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Communication Layer                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           WebSocket + Socket.IO                        │ │
│  │     (实时视频流 + 姿态数据 + 控制指令)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Backend (Flask + OpenCV)                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   AI算法    │ │  视频处理   │ │  数据管理   │           │
│  │ (姿态检测)   │ │ (实时流)    │ │ (用户数据)   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 🧩 模块化Store架构

系统采用模块化的Pinia store设计，将不同职责分离到专门的store模块：

- **🔗 ConnectionStore**: WebSocket连接状态和实时数据管理
- **👤 PatientStore**: 患者信息和身份验证管理
- **🏃‍♂️ TrainingStore**: 训练动作和进度管理
- **⚙️ WorkflowStore**: 工作流状态转换管理
- **📢 NotificationStore**: 消息通知管理

### 🔄 状态机工作流

系统基于有限状态机管理整个训练流程：

```
waiting → introduction → preparation → training → reporting → waiting
   ↑                                      ↓
   └──────────── error/pause ←────────────┘
```

---

## 🚀 快速开始

### 📋 环境要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+

### 📥 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd digital-screen/frontend
```

2. **安装依赖**
```bash
npm install
# 或使用 yarn
yarn install
```

3. **环境配置**
```bash
# 复制环境配置文件
cp .env.example .env.local

# 编辑配置文件，设置后端服务地址
# VITE_API_BASE_URL=http://localhost:5000
# VITE_WS_URL=ws://localhost:5000
```

4. **启动开发服务器**
```bash
npm run dev
# 或
yarn dev
```

5. **访问应用**
   - 开发服务器: http://localhost:3000
   - 确保后端服务已启动并运行在配置的端口

### 🔧 开发模式特性

- **热重载**: 代码修改后自动刷新
- **状态调试**: 开发工具中的Pinia状态监控
- **错误提示**: 详细的开发错误信息
- **性能监控**: 组件渲染性能分析

---

## 👨‍💻 开发指南

### 📁 项目结构

```
frontend/
├── public/                 # 静态资源
│   └── resources/         # 媒体文件
├── src/
│   ├── components/        # 可复用组件
│   │   ├── touchless/    # 无接触控制组件
│   │   ├── VideoStream.vue
│   │   ├── PoseOverlay.vue
│   │   └── CelebrationAnimation.vue
│   ├── composables/       # 组合式函数
│   │   ├── detectors/    # 动作检测器
│   │   ├── useActionEvaluationEngine.js
│   │   ├── useEnhancedTrainingSession.js
│   │   └── useStateTransition.js
│   ├── stores/           # Pinia状态管理
│   │   ├── connection.js
│   │   ├── patient.js
│   │   ├── training.js
│   │   ├── workflow.js
│   │   └── index.js
│   ├── views/            # 页面组件
│   │   ├── LoginView.vue
│   │   ├── TrainingView.vue
│   │   ├── ReportView.vue
│   │   └── ErrorView.vue
│   ├── router/           # 路由配置
│   ├── services/         # 服务层
│   ├── utils/            # 工具函数
│   ├── App.vue          # 根组件
│   └── main.js          # 应用入口
├── package.json
├── vite.config.js       # Vite配置
├── tailwind.config.js   # TailwindCSS配置
└── README.md
```

### 🧱 核心组件说明

#### 📄 页面组件 (Views)
- **LoginView.vue**: 用户登录和身份识别页面
  - 实时视频流显示
  - 人脸检测和身份验证
  - 登录状态管理和动画效果

- **TrainingView.vue**: 主训练界面
  - 四象限布局设计
  - 实时姿态可视化
  - 动作评估和反馈系统
  - 无接触异常处理集成

- **ReportView.vue**: 训练报告页面
  - 训练数据可视化
  - 性能统计图表
  - 数据导出功能

- **ErrorView.vue**: 错误处理页面
  - 系统错误显示
  - 自动重连机制
  - 用户友好的错误提示

#### 🧩 可复用组件 (Components)
- **VideoStream.vue**: 视频流组件
  - 实时摄像头画面显示
  - 人脸检测框渲染
  - 帧率监控和错误处理

- **PoseOverlay.vue**: 姿态可视化组件
  - 关键点和骨架线渲染
  - 动作轨迹可视化
  - 目标区域指示

- **CelebrationAnimation.vue**: 庆祝动画组件
  - 动作完成庆祝效果
  - 粒子动画系统
  - 音效同步播放

#### 🔧 组合式函数 (Composables)
- **useActionEvaluationEngine.js**: 动作评估引擎
  - 动态评估器加载
  - 多动作类型支持
  - 实时评分计算

- **useEnhancedTrainingSession.js**: 训练会话管理
  - 完整训练流程控制
  - 评估引擎集成
  - 音频反馈管理

- **useStateTransition.js**: 状态转换管理
  - 工作流状态控制
  - 状态验证和转换
  - 错误处理机制

### 📊 数据流架构

```
用户操作 → Vue组件 → Composables → Pinia Store → WebSocket → 后端服务
    ↑                                    ↓
    └──── UI更新 ←── 响应式数据 ←── 实时数据接收 ←──────┘
```

### 🎨 设计系统

- **色彩方案**: 基于医疗健康的蓝绿色调
- **动画效果**: 流畅的过渡和反馈动画
- **响应式设计**: 适配不同屏幕尺寸
- **无障碍支持**: 符合WCAG 2.1标准

---

## 🔌 API文档

### 📡 WebSocket通信协议

系统使用Socket.IO进行实时双向通信，主要消息类型：

#### 📥 接收消息 (从后端)

**视频帧数据**
```javascript
{
  "type": "frame_data",
  "data": {
    "frame": "base64_encoded_image",
    "frame_count": 12345,
    "fps": 30,
    "pose_keypoints": [...], // 133个关键点
    "patient_id": "user_001",
    "face_box": [x, y, width, height]
  }
}
```

**系统状态更新**
```javascript
{
  "type": "system_status",
  "data": {
    "status": "ready|training|error",
    "message": "状态描述",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

**训练结果**
```javascript
{
  "type": "training_result",
  "data": {
    "action_id": "shoulder_touch_001",
    "score": 85,
    "completion_time": 15.5,
    "feedback": "动作完成良好"
  }
}
```

#### 📤 发送消息 (到后端)

**开始训练**
```javascript
{
  "type": "start_training",
  "data": {
    "patient_id": "user_001",
    "action_list": ["shoulder_touch", "arm_raise"],
    "difficulty": "medium"
  }
}
```

**暂停/恢复训练**
```javascript
{
  "type": "pause_training",
  "data": {
    "action": "pause|resume",
    "reason": "user_request|timeout|error"
  }
}
```

### 🔗 HTTP API接口

**获取系统状态**
```
GET /api/system/status
Response: {
  "status": "healthy",
  "services": {
    "camera": true,
    "ai_model": true,
    "database": true
  }
}
```

**获取训练历史**
```
GET /api/training/history?patient_id=user_001
Response: {
  "data": [...],
  "total": 50,
  "page": 1
}
```

---

## 🛠️ 开发规范

### 📝 代码规范

**Vue组件规范**
```javascript
// 推荐的组件结构
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 1. 导入依赖
import { ref, computed, onMounted } from 'vue'
import { useStore } from '@/stores/example'

// 2. 定义props和emits
const props = defineProps({...})
const emit = defineEmits(['update'])

// 3. 响应式数据
const state = ref(null)

// 4. 计算属性
const computedValue = computed(() => ...)

// 5. 方法定义
const handleAction = () => {...}

// 6. 生命周期
onMounted(() => {...})
</script>

<style scoped>
/* 组件样式 */
</style>
```

**Composables规范**
```javascript
// 组合式函数命名以use开头
export function useFeatureName() {
  // 响应式状态
  const state = ref(null)

  // 计算属性
  const computed = computed(() => ...)

  // 方法
  const method = () => {...}

  // 生命周期清理
  onUnmounted(() => {
    // 清理资源
  })

  // 返回公共API
  return {
    state,
    computed,
    method
  }
}
```

### 🧪 测试策略

**单元测试**
```bash
# 运行单元测试
npm run test:unit

# 测试覆盖率
npm run test:coverage
```

**组件测试**
```javascript
import { mount } from '@vue/test-utils'
import VideoStream from '@/components/VideoStream.vue'

describe('VideoStream', () => {
  it('should render video stream', () => {
    const wrapper = mount(VideoStream)
    expect(wrapper.find('.video-stream-wrapper').exists()).toBe(true)
  })
})
```

### 🔍 调试工具

**开发环境调试**
- Vue DevTools: 组件状态和事件监控
- Pinia DevTools: 状态管理调试
- Network面板: WebSocket消息监控
- Console: 详细日志输出

**性能监控**
```javascript
// 性能监控示例
import { performance } from '@/utils/performance'

performance.mark('action-start')
// 执行动作评估
performance.mark('action-end')
performance.measure('action-evaluation', 'action-start', 'action-end')
```

---

## 🚀 构建与部署

### 📦 构建配置

**开发构建**
```bash
npm run dev
# 启动开发服务器，支持热重载
```

**生产构建**
```bash
npm run build
# 生成优化的生产版本到 dist/ 目录
```

**预览构建**
```bash
npm run preview
# 本地预览生产构建结果
```

### 🌐 部署方案

**静态部署**
```bash
# 构建生产版本
npm run build

# 部署到静态服务器
# 将 dist/ 目录内容上传到服务器
```

**Docker部署**
```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

**环境变量配置**
```bash
# .env.production
VITE_API_BASE_URL=https://api.rehabilitation.com
VITE_WS_URL=wss://api.rehabilitation.com
VITE_APP_TITLE=智能康复训练系统
```

### 📊 性能优化

**代码分割**
- 路由级别的懒加载
- 组件按需导入
- 第三方库分离打包

**资源优化**
- 图片压缩和WebP格式
- CSS和JS压缩
- Gzip压缩启用

**缓存策略**
- 静态资源长期缓存
- API数据适当缓存
- Service Worker离线支持

---

## 🔧 故障排除

### ❗ 常见问题

**WebSocket连接失败**
```bash
# 检查后端服务状态
curl http://localhost:5000/health

# 检查网络连接
ping localhost

# 查看浏览器控制台错误信息
```

**视频流无法显示**
- 检查摄像头权限设置
- 确认后端视频服务运行正常
- 查看网络请求是否成功

**姿态检测不准确**
- 确保光线充足
- 检查摄像头位置和角度
- 验证AI模型加载状态

**性能问题**
- 检查内存使用情况
- 监控CPU占用率
- 优化组件渲染频率

### 📋 调试检查清单

1. **环境检查**
   - [ ] Node.js版本 >= 16.0.0
   - [ ] 依赖包安装完整
   - [ ] 环境变量配置正确

2. **服务检查**
   - [ ] 后端服务运行正常
   - [ ] WebSocket连接建立
   - [ ] 摄像头设备可用

3. **网络检查**
   - [ ] API接口可访问
   - [ ] WebSocket端口开放
   - [ ] 防火墙配置正确

### 📞 技术支持

如遇到问题，请按以下步骤获取帮助：

1. **查看日志**: 检查浏览器控制台和网络面板
2. **重现问题**: 记录详细的操作步骤
3. **收集信息**: 系统版本、浏览器版本、错误信息
4. **联系支持**: 提供完整的问题描述和环境信息

---

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 📋 贡献规范

- 遵循现有代码风格
- 添加适当的测试用例
- 更新相关文档
- 确保所有测试通过

---

## 📚 相关文档

- [Vue 3 官方文档](https://vuejs.org/)
- [Vite 构建工具](https://vitejs.dev/)
- [Element Plus 组件库](https://element-plus.org/)
- [TailwindCSS 样式框架](https://tailwindcss.com/)
- [Pinia 状态管理](https://pinia.vuejs.org/)

---

<div align="center">

**智能康复训练系统** - 让康复训练更智能、更高效

Made with ❤️ by Development Team

</div>