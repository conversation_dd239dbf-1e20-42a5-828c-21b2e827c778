/**
 * 音频优先级队列管理器
 * 负责音频播放队列的智能管理和优先级调度
 */

export class AudioPriorityQueue {
  constructor() {
    this.queue = []
    this.isProcessing = false
    this.currentAudio = null
    this.lastPlayedTimes = new Map() // 记录每个音频的最后播放时间
    this.playedInCurrentAction = new Set() // 记录当前动作中已播放的音频
  }

  /**
   * 添加音频到队列
   * @param {Object} audioItem - 音频项目
   * @param {string} audioItem.audioKey - 音频键名
   * @param {number} audioItem.priority - 优先级
   * @param {number} audioItem.cooldown - 冷却时间
   * @param {Object} audioItem.options - 播放选项
   * @returns {boolean} 是否成功添加
   */
  enqueue(audioItem) {
    const { audioKey, priority, cooldown, options = {} } = audioItem
    
    // 检查冷却时间
    if (!this.canPlay(audioKey, cooldown)) {
      console.log(`[AudioQueue] 音频 ${audioKey} 在冷却期内，跳过`)
      return false
    }

    // 检查是否在当前动作中已播放（仅对指导类音频）
    if (this.isGuidanceAudio(audioKey) && this.playedInCurrentAction.has(audioKey)) {
      console.log(`[AudioQueue] 指导音频 ${audioKey} 在当前动作中已播放，跳过`)
      return false
    }

    // 创建队列项目
    const queueItem = {
      audioKey,
      priority,
      cooldown,
      options,
      timestamp: Date.now(),
      id: this.generateId()
    }

    // 插入到正确的位置（按优先级排序）
    this.insertByPriority(queueItem)
    
    console.log(`[AudioQueue] 音频 ${audioKey} 已加入队列，优先级: ${priority}`)
    return true
  }

  /**
   * 按优先级插入队列项目
   * @param {Object} queueItem - 队列项目
   */
  insertByPriority(queueItem) {
    let insertIndex = this.queue.length
    
    // 找到正确的插入位置
    for (let i = 0; i < this.queue.length; i++) {
      if (queueItem.priority < this.queue[i].priority) {
        insertIndex = i
        break
      }
    }
    
    this.queue.splice(insertIndex, 0, queueItem)
  }

  /**
   * 从队列中取出下一个音频
   * @returns {Object|null} 下一个音频项目
   */
  dequeue() {
    if (this.queue.length === 0) {
      return null
    }
    
    const item = this.queue.shift()
    console.log(`[AudioQueue] 从队列取出音频: ${item.audioKey}`)
    return item
  }

  /**
   * 检查音频是否可以播放（冷却时间检查）
   * @param {string} audioKey - 音频键名
   * @param {number} cooldown - 冷却时间
   * @returns {boolean} 是否可以播放
   */
  canPlay(audioKey, cooldown) {
    if (cooldown === 0) return true
    
    const lastPlayTime = this.lastPlayedTimes.get(audioKey)
    if (!lastPlayTime) return true
    
    const timeSinceLastPlay = Date.now() - lastPlayTime
    return timeSinceLastPlay >= cooldown
  }

  /**
   * 标记音频已播放
   * @param {string} audioKey - 音频键名
   */
  markAsPlayed(audioKey) {
    this.lastPlayedTimes.set(audioKey, Date.now())
    
    // 如果是指导类音频，添加到当前动作已播放集合
    if (this.isGuidanceAudio(audioKey)) {
      this.playedInCurrentAction.add(audioKey)
    }
  }

  /**
   * 检查是否为指导类音频
   * @param {string} audioKey - 音频键名
   * @returns {boolean} 是否为指导类音频
   */
  isGuidanceAudio(audioKey) {
    return audioKey.includes('shoulder_touch') || 
           audioKey.includes('arm_raise') || 
           audioKey.includes('finger_touch') || 
           audioKey.includes('palm_flip')
  }

  /**
   * 清空当前动作的播放记录
   */
  clearCurrentActionPlayed() {
    this.playedInCurrentAction.clear()
    console.log('[AudioQueue] 已清空当前动作播放记录')
  }

  /**
   * 设置当前播放的音频
   * @param {string} audioKey - 音频键名
   */
  setCurrentAudio(audioKey) {
    this.currentAudio = audioKey
  }

  /**
   * 清除当前播放的音频
   */
  clearCurrentAudio() {
    this.currentAudio = null
  }

  /**
   * 获取当前播放的音频
   * @returns {string|null} 当前播放的音频键名
   */
  getCurrentAudio() {
    return this.currentAudio
  }

  /**
   * 检查是否有音频在播放
   * @returns {boolean} 是否有音频在播放
   */
  isPlaying() {
    return this.currentAudio !== null
  }

  /**
   * 设置处理状态
   * @param {boolean} processing - 是否正在处理
   */
  setProcessing(processing) {
    this.isProcessing = processing
  }

  /**
   * 检查是否正在处理队列
   * @returns {boolean} 是否正在处理
   */
  getProcessing() {
    return this.isProcessing
  }

  /**
   * 获取队列长度
   * @returns {number} 队列长度
   */
  getQueueLength() {
    return this.queue.length
  }

  /**
   * 清空队列
   */
  clear() {
    this.queue = []
    this.currentAudio = null
    this.isProcessing = false
    console.log('[AudioQueue] 队列已清空')
  }

  /**
   * 移除指定优先级以下的音频（用于紧急音频插队）
   * @param {number} priority - 优先级阈值
   */
  removeByPriority(priority) {
    const originalLength = this.queue.length
    this.queue = this.queue.filter(item => item.priority <= priority)
    const removedCount = originalLength - this.queue.length
    
    if (removedCount > 0) {
      console.log(`[AudioQueue] 移除了 ${removedCount} 个低优先级音频`)
    }
  }

  /**
   * 获取队列状态信息
   * @returns {Object} 队列状态
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      currentAudio: this.currentAudio,
      nextAudio: this.queue.length > 0 ? this.queue[0].audioKey : null,
      playedInCurrentAction: Array.from(this.playedInCurrentAction)
    }
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 重置所有播放记录（用于新会话开始）
   */
  resetPlayHistory() {
    this.lastPlayedTimes.clear()
    this.playedInCurrentAction.clear()
    console.log('[AudioQueue] 播放历史已重置')
  }
}
