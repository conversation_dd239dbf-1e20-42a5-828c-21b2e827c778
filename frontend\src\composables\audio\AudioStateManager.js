/**
 * 音频状态管理器
 * 负责训练状态跟踪和智能音频触发决策
 */

export class AudioStateManager {
  constructor() {
    this.currentState = 'IDLE'
    this.previousState = null
    this.stateHistory = []
    this.stateChangeTime = 0
    this.currentAction = null
    this.currentScore = 0
    this.statePlayedFlags = new Map() // 记录每个状态是否已播放音频
    this.actionPhase = 'loading' // loading, executing, completing
    this.initializeStateMapping()
  }

  /**
   * 初始化简化的状态映射规则
   */
  initializeStateMapping() {
    // 简化的状态到音频映射规则 - 每个动作只有3-4个关键音频节点
    this.STATE_AUDIO_MAPPING = {
      // 关键状态1: 保持阶段 - 每个动作只播放一次
      'HOLDING': {
        condition: () => true,
        audioKey: 'hold',
        playOnce: true
      },

      'TOUCHING': {
        condition: () => true,
        audioKey: 'hold',
        playOnce: true
      },

      // 关键状态2: 返回阶段 - 每个动作只播放一次
      'RETURNING': {
        condition: () => true,
        audioKey: 'return',
        playOnce: true
      },

      'LOWERING': {
        condition: () => true,
        audioKey: 'return',
        playOnce: true
      },

      // 关键状态3: 完成状态 - 根据分数评价
      'COMPLETED': {
        condition: () => true,
        audioKey: (score) => {
          if (score >= 90) return 'excellent'
          if (score >= 70) return 'good'
          return 'fair'
        },
        playOnce: true,
        includeSound: 'success'
      },

      // 错误状态 - 简化处理
      'ERROR': {
        condition: () => true,
        audioKey: 'error',
        playOnce: false,
        includeSound: 'error',
        specialCooldown: 3000 // 3秒特殊冷却
      }
    }

    // 简化方案：移除复杂的详细指导和鼓励语音逻辑
    console.log('[AudioStateManager] 使用简化的音频反馈策略')
  }

  /**
   * 更新当前状态
   * @param {string} state - 新状态
   * @param {number} score - 当前分数
   * @param {string} feedback - 反馈文本
   * @returns {Object} 音频触发结果
   */
  updateState(state, score = 0, feedback = '') {
    const previousState = this.currentState
    const isStateChanged = state !== previousState
    // 更新状态信息
    if (isStateChanged) {
      this.previousState = previousState
      this.currentState = state
      this.stateChangeTime = Date.now()
      this.stateHistory.push({
        state,
        timestamp: this.stateChangeTime,
        score,
        feedback
      })
      console.log(`[AudioStateManager] 状态变化: ${previousState} -> ${state}, 分数: ${score}`)
    }
    
    this.currentScore = score
    
    // 生成音频触发结果
    return this.generateAudioTriggers(state, score, feedback, isStateChanged)
  }

  /**
   * 生成音频触发结果
   * @param {string} state - 当前状态
   * @param {number} score - 当前分数
   * @param {string} feedback - 反馈文本
   * @param {boolean} isStateChanged - 状态是否改变
   * @returns {Object} 音频触发结果
   */
  generateAudioTriggers(state, score, feedback, isStateChanged) {
    const triggers = {
      primary: null,      // 主要音频（状态反馈）
      sound: null         // 音效
    }

    // 简化逻辑：只处理关键状态的音频反馈
    if (isStateChanged) {
      const stateMapping = this.STATE_AUDIO_MAPPING[state]
      if (stateMapping && stateMapping.condition(state, score, feedback)) {
        const stateKey = `${state}_${this.currentAction?.action_id || 'unknown'}`
        // 检查是否需要只播放一次
        if (!stateMapping.playOnce || !this.statePlayedFlags.has(stateKey)) {
          let audioKey = stateMapping.audioKey
          if (typeof audioKey === 'function') {
            audioKey = audioKey(score)
          }
          if (audioKey) {
            triggers.primary = {
              audioKey,
              reason: `状态变化: ${state}`,
              priority: 'medium'
            }
            // 标记已播放
            if (stateMapping.playOnce) {
              this.statePlayedFlags.set(stateKey, true)
            }
            // 添加音效
            if (stateMapping.includeSound) {
              triggers.sound = stateMapping.includeSound
            }
          }
        }
      }
    }

    return triggers
  }

  // 简化方案：移除详细指导和鼓励语音检查函数

  /**
   * 设置当前动作
   */
  setCurrentAction(action) {
    this.currentAction = action
    this.actionPhase = 'loading'
    
    // 清除当前动作相关的播放标记
    this.clearCurrentActionFlags()
    
    console.log(`[AudioStateManager] 设置当前动作: ${action?.action_name}`)
  }

  /**
   * 清除当前动作的播放标记
   */
  clearCurrentActionFlags() {
    if (!this.currentAction) return
    
    const actionId = this.currentAction.action_id
    const keysToRemove = []
    
    for (const key of this.statePlayedFlags.keys()) {
      if (key.includes(actionId)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => this.statePlayedFlags.delete(key))
    console.log(`[AudioStateManager] 已清除 ${keysToRemove.length} 个动作相关标记`)
  }

  /**
   * 设置动作阶段
   */
  setActionPhase(phase) {
    this.actionPhase = phase
    console.log(`[AudioStateManager] 动作阶段: ${phase}`)
  }

  /**
   * 重置状态管理器
   */
  reset() {
    this.currentState = 'IDLE'
    this.previousState = null
    this.stateHistory = []
    this.currentAction = null
    this.currentScore = 0
    this.statePlayedFlags.clear()
    this.actionPhase = 'loading'
    console.log('[AudioStateManager] 状态管理器已重置')
  }

  /**
   * 获取状态统计信息
   */
  getStats() {
    return {
      currentState: this.currentState,
      previousState: this.previousState,
      currentScore: this.currentScore,
      actionPhase: this.actionPhase,
      stateHistoryLength: this.stateHistory.length,
      playedFlagsCount: this.statePlayedFlags.size
    }
  }
}
