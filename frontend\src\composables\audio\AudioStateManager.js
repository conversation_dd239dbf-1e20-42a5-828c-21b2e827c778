/**
 * 音频状态管理器
 * 负责训练状态跟踪和智能音频触发决策
 */

export class AudioStateManager {
  constructor() {
    this.currentState = 'IDLE'
    this.previousState = null
    this.stateHistory = []
    this.stateChangeTime = 0
    this.currentAction = null
    this.currentScore = 0
    this.statePlayedFlags = new Map() // 记录每个状态是否已播放音频
    this.actionPhase = 'loading' // loading, executing, completing
    this.initializeStateMapping()
  }

  /**
   * 初始化状态映射规则
   */
  initializeStateMapping() {
    // 状态到音频的映射规则
    this.STATE_AUDIO_MAPPING = {
      // 基础状态
      'IDLE': {
        condition: (state, score, feedback) => feedback && feedback.includes('准备'),
        audioKey: 'prepare',
        playOnce: true
      },
      
      // 动作执行状态
      'MOVING_TO_TARGET': {
        condition: () => false, // 不播放音频，避免干扰
        audioKey: null,
        playOnce: false
      },
      
      'APPROACHING': {
        condition: () => false, // 不播放音频，避免干扰
        audioKey: null,
        playOnce: false
      },
      
      'RAISING': {
        condition: () => false, // 不播放音频，避免干扰
        audioKey: null,
        playOnce: false
      },
      
      // 关键状态 - 需要音频反馈
      'HOLDING': {
        condition: () => true,
        audioKey: 'hold',
        playOnce: true
      },
      
      'TOUCHING': {
        condition: () => true,
        audioKey: 'hold',
        playOnce: true
      },
      
      'RETURNING': {
        condition: () => true,
        audioKey: 'return',
        playOnce: true
      },
      
      'LOWERING': {
        condition: () => true,
        audioKey: 'return',
        playOnce: true
      },
      
      // 完成状态
      'COMPLETED': {
        condition: () => true,
        audioKey: (score) => {
          if (score >= 90) return 'excellent'
          if (score >= 70) return 'good'
          return 'fair'
        },
        playOnce: true,
        includeSound: 'success'
      },
      
      // 错误状态
      'ERROR': {
        condition: () => true,
        audioKey: 'error',
        playOnce: false, // 错误状态可以重复播放，但有特殊的频率控制
        includeSound: 'error',
        specialCooldown: 3000 // 3秒特殊冷却
      }
    }

    // 详细指导触发条件
    this.DETAILED_GUIDANCE_CONDITIONS = {
      // 肩膀触摸详细指导
      shoulder_touch: {
        closer: (state, score) => state === 'MOVING_TO_TARGET' && score < 30,
        left_closer: (state, score, feedback, action) => 
          action?.side === 'left' && state === 'MOVING_TO_TARGET' && score < 30,
        right_closer: (state, score, feedback, action) => 
          action?.side === 'right' && state === 'MOVING_TO_TARGET' && score < 30
      },
      
      // 手臂上举详细指导
      arm_raise: {
        left_higher: (state, score, feedback, action) => 
          action?.side === 'left' && state === 'RAISING' && score < 40,
        left_straight: (state, score, feedback, action) => 
          action?.side === 'left' && (state === 'RAISING' || state === 'HOLDING') && score < 50,
        right_higher: (state, score, feedback, action) => 
          action?.side === 'right' && state === 'RAISING' && score < 40,
        right_straight: (state, score, feedback, action) => 
          action?.side === 'right' && (state === 'RAISING' || state === 'HOLDING') && score < 50
      },
      
      // 指尖对触详细指导
      finger_touch: {
        closer: (state, score) => state === 'APPROACHING' && score < 35
      },
      
      // 手掌翻转详细指导
      palm_flip: {
        left_range: (state, score, feedback, action) => 
          action?.side === 'left' && score < 40,
        right_range: (state, score, feedback, action) => 
          action?.side === 'right' && score < 40
      },
      
      // 通用指导
      general: {
        stability: (state, score) => score > 0 && score < 60 && 
          ['HOLDING', 'TOUCHING'].includes(state),
        frame_complete: (state, score) => state === 'COMPLETED' && score >= 80
      }
    }

    // 鼓励语音触发条件
    this.ENCOURAGEMENT_CONDITIONS = {
      keep_going: (state, score) => score > 30 && score < 60 && 
        ['MOVING_TO_TARGET', 'RAISING', 'APPROACHING'].includes(state),
      almost_there: (state, score) => score > 60 && score < 85 && 
        ['HOLDING', 'TOUCHING'].includes(state),
      good_progress: (state, score) => score > 50 && 
        ['RETURNING', 'LOWERING'].includes(state)
    }
  }

  /**
   * 更新当前状态
   * @param {string} state - 新状态
   * @param {number} score - 当前分数
   * @param {string} feedback - 反馈文本
   * @returns {Object} 音频触发结果
   */
  updateState(state, score = 0, feedback = '') {
    const previousState = this.currentState
    const isStateChanged = state !== previousState
    // 更新状态信息
    if (isStateChanged) {
      this.previousState = previousState
      this.currentState = state
      this.stateChangeTime = Date.now()
      this.stateHistory.push({
        state,
        timestamp: this.stateChangeTime,
        score,
        feedback
      })
      console.log(`[AudioStateManager] 状态变化: ${previousState} -> ${state}, 分数: ${score}`)
    }
    
    this.currentScore = score
    
    // 生成音频触发结果
    return this.generateAudioTriggers(state, score, feedback, isStateChanged)
  }

  /**
   * 生成音频触发结果
   * @param {string} state - 当前状态
   * @param {number} score - 当前分数
   * @param {string} feedback - 反馈文本
   * @param {boolean} isStateChanged - 状态是否改变
   * @returns {Object} 音频触发结果
   */
  generateAudioTriggers(state, score, feedback, isStateChanged) {
    const triggers = {
      primary: null,      // 主要音频（状态反馈）
      detailed: null,     // 详细指导音频
      encouragement: null, // 鼓励音频
      sound: null         // 音效
    }

    // 1. 处理主要状态音频
    if (isStateChanged) {
      const stateMapping = this.STATE_AUDIO_MAPPING[state]
      if (stateMapping && stateMapping.condition(state, score, feedback)) {
        const stateKey = `${state}_${this.currentAction?.action_id || 'unknown'}`
        // 检查是否需要只播放一次
        if (!stateMapping.playOnce || !this.statePlayedFlags.has(stateKey)) {
          let audioKey = stateMapping.audioKey
          if (typeof audioKey === 'function') {
            audioKey = audioKey(score)
          }
          if (audioKey) {
            triggers.primary = {
              audioKey,
              reason: `状态变化: ${state}`,
              priority: 'medium'
            }
            // 标记已播放
            if (stateMapping.playOnce) {
              this.statePlayedFlags.set(stateKey, true)
            }
            
            // 添加音效
            if (stateMapping.includeSound) {
              triggers.sound = stateMapping.includeSound
            }
          }
        }
      }
    }

    // 2. 处理详细指导音频
    if (this.currentAction) {
      const detailedGuidance = this.checkDetailedGuidance(state, score, feedback)
      if (detailedGuidance) {
        triggers.detailed = {
          audioKey: detailedGuidance,
          reason: '详细指导触发',
          priority: 'medium_high'
        }
      }
    }

    // 3. 处理鼓励音频（较低频率）
    const encouragement = this.checkEncouragement(state, score)
    if (encouragement) {
      triggers.encouragement = {
        audioKey: encouragement,
        reason: '鼓励语音触发',
        priority: 'low'
      }
    }

    return triggers
  }

  /**
   * 检查详细指导触发条件
   */
  checkDetailedGuidance(state, score, feedback) {
    if (!this.currentAction) return null
    
    const actionType = this.currentAction.action_type
    const guidanceRules = this.DETAILED_GUIDANCE_CONDITIONS[actionType]
    
    if (!guidanceRules) return null
    
    for (const [guidanceType, condition] of Object.entries(guidanceRules)) {
      if (condition(state, score, feedback, this.currentAction)) {
        const guidanceKey = `${actionType}_${guidanceType}`
        
        // 检查是否已在当前动作中播放过
        const playedKey = `detailed_${guidanceKey}_${this.currentAction.action_id}`
        if (!this.statePlayedFlags.has(playedKey)) {
          this.statePlayedFlags.set(playedKey, true)
          return guidanceKey
        }
      }
    }
    
    return null
  }

  /**
   * 检查鼓励语音触发条件
   */
  checkEncouragement(state, score) {
    for (const [encouragementType, condition] of Object.entries(this.ENCOURAGEMENT_CONDITIONS)) {
      if (condition(state, score)) {
        // 鼓励语音有更长的冷却时间，避免过于频繁
        const encouragementKey = `encouragement_${encouragementType}`
        const lastPlayTime = this.statePlayedFlags.get(encouragementKey)
        const currentTime = Date.now()
        
        if (!lastPlayTime || currentTime - lastPlayTime > 10000) { // 10秒冷却
          this.statePlayedFlags.set(encouragementKey, currentTime)
          return encouragementType
        }
      }
    }
    
    return null
  }

  /**
   * 设置当前动作
   */
  setCurrentAction(action) {
    this.currentAction = action
    this.actionPhase = 'loading'
    
    // 清除当前动作相关的播放标记
    this.clearCurrentActionFlags()
    
    console.log(`[AudioStateManager] 设置当前动作: ${action?.action_name}`)
  }

  /**
   * 清除当前动作的播放标记
   */
  clearCurrentActionFlags() {
    if (!this.currentAction) return
    
    const actionId = this.currentAction.action_id
    const keysToRemove = []
    
    for (const key of this.statePlayedFlags.keys()) {
      if (key.includes(actionId)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => this.statePlayedFlags.delete(key))
    console.log(`[AudioStateManager] 已清除 ${keysToRemove.length} 个动作相关标记`)
  }

  /**
   * 设置动作阶段
   */
  setActionPhase(phase) {
    this.actionPhase = phase
    console.log(`[AudioStateManager] 动作阶段: ${phase}`)
  }

  /**
   * 重置状态管理器
   */
  reset() {
    this.currentState = 'IDLE'
    this.previousState = null
    this.stateHistory = []
    this.currentAction = null
    this.currentScore = 0
    this.statePlayedFlags.clear()
    this.actionPhase = 'loading'
    console.log('[AudioStateManager] 状态管理器已重置')
  }

  /**
   * 获取状态统计信息
   */
  getStats() {
    return {
      currentState: this.currentState,
      previousState: this.previousState,
      currentScore: this.currentScore,
      actionPhase: this.actionPhase,
      stateHistoryLength: this.stateHistory.length,
      playedFlagsCount: this.statePlayedFlags.size
    }
  }
}
