/**
 * 无接触式动作超时检测系统
 * 基于时间维度检测患者是否无法完成动作，并自动跳过困难动作
 */

import { ref, computed } from "vue";

// 超时配置
const TIMEOUT_CONFIG = {
  maxAttemptTime: 30000, // 30秒总超时
  warningTime: 20000, // 20秒时开始倒计时警告（最后10秒）
  progressCheckInterval: 1000, // 每1秒检查一次进展
};

export function useTouchlessActionTimeout() {
  // 检测状态
  const isDetectionActive = ref(false);
  const isPaused = ref(false);
  const pausedTime = ref(null);
  const totalPausedDuration = ref(0);
  const currentAction = ref(null);
  const actionStartTime = ref(null);
  const hasWarned = ref(false);
  const progressCheckTimer = ref(null);

  // 回调函数
  const callbacks = ref({
    onTimeout: null,
    onWarning: null,
  });

  // 计算属性
  const elapsedTime = computed(() => {
    if (!actionStartTime.value) return 0;
    const currentTime = isPaused.value ? pausedTime.value : Date.now();
    return currentTime - actionStartTime.value - totalPausedDuration.value;
  });

  const shouldWarn = computed(() => {
    return elapsedTime.value > TIMEOUT_CONFIG.warningTime && !hasWarned.value;
  });

  const remainingTime = computed(() => {
    return Math.max(0, TIMEOUT_CONFIG.maxAttemptTime - elapsedTime.value);
  });

  const countdownSeconds = computed(() => {
    return Math.ceil(remainingTime.value / 1000);
  });

  const shouldTimeout = computed(() => {
    return elapsedTime.value > TIMEOUT_CONFIG.maxAttemptTime;
  });

  /**
   * 启动超时检测
   * @param {Object} action - 当前动作对象
   * @param {Object} options - 回调函数配置
   */
  const startDetection = (action, options = {}) => {

    // 如果已经在检测同一个动作，直接返回
    if (isDetectionActive.value && currentAction.value?.action_id === action.action_id) {
      console.log(`[TouchlessTimeout] 已在检测同一动作: ${action.action_name}，跳过重复启动`);
      return;
    }
    if (isDetectionActive.value) {
      stopDetection();
    }
    console.log(`[TouchlessTimeout] 🚀 开始检测动作: ${action.action_name}`);
    // 初始化状态
    currentAction.value = action;
    const startTime = Date.now();
    actionStartTime.value = startTime;
    hasWarned.value = false;
    isPaused.value = false;
    pausedTime.value = null;
    totalPausedDuration.value = 0;
    isDetectionActive.value = true;
    console.log(`[TouchlessTimeout] ⏰ 设置开始时间: ${startTime} (${new Date(startTime).toLocaleTimeString()})`);
    // 设置回调函数
    callbacks.value = {
      onTimeout: options.onTimeout || (() => {}),
      onWarning: options.onWarning || (() => {}),
    };
    // 启动进展检查定时器
    startProgressCheck();
  };

  /**
   * 停止超时检测
   */
  const stopDetection = () => {
    if (!isDetectionActive.value) {
      return;
    }
    // 清理定时器
    if (progressCheckTimer.value) {
      clearInterval(progressCheckTimer.value);
      progressCheckTimer.value = null;
      console.log("[TouchlessTimeout] 定时器已清理");
    }

    // 重置状态
    isDetectionActive.value = false;
    isPaused.value = false;
    pausedTime.value = null;
    totalPausedDuration.value = 0;
    currentAction.value = null;
    actionStartTime.value = null;
    hasWarned.value = false;

    // 清空回调
    callbacks.value = {
      onTimeout: null,
      onWarning: null,
    };
    console.log("[TouchlessTimeout] 状态已重置");
  };

  /**
   * 暂停超时检测
   */
  const pauseDetection = () => {
    if (!isDetectionActive.value) {
      console.log('[TouchlessTimeout] 检测未激活，无法暂停');
      return;
    }

    if (isPaused.value) {
      console.log('[TouchlessTimeout] 已经暂停，跳过重复暂停');
      return;
    }

    console.log('[TouchlessTimeout] 🛑 暂停超时检测');
    isPaused.value = true;
    pausedTime.value = Date.now();

    // 暂停定时器
    if (progressCheckTimer.value) {
      console.log('[TouchlessTimeout] 清除进度检查定时器');
      clearInterval(progressCheckTimer.value);
      progressCheckTimer.value = null;
    } else {
      console.log('[TouchlessTimeout] 进度检查定时器不存在');
    }

    console.log('[TouchlessTimeout] ✅ 超时检测已暂停');
  };

  /**
   * 恢复超时检测
   */
  const resumeDetection = () => {
    console.log('[TouchlessTimeout] ▶️ 尝试恢复超时检测');
    console.log('[TouchlessTimeout] 当前状态 - 激活:', isDetectionActive.value, '暂停:', isPaused.value);

    if (!isDetectionActive.value) {
      console.log('[TouchlessTimeout] 检测未激活，无法恢复');
      return;
    }

    if (!isPaused.value) {
      console.log('[TouchlessTimeout] 未暂停，无需恢复');
      return;
    }

    console.log('[TouchlessTimeout] ▶️ 恢复超时检测');

    // 计算暂停时长并累加
    if (pausedTime.value) {
      const pauseDuration = Date.now() - pausedTime.value;
      totalPausedDuration.value += pauseDuration;
      console.log('[TouchlessTimeout] 暂停时长:', Math.floor(pauseDuration/1000), '秒，总暂停时长:', Math.floor(totalPausedDuration.value/1000), '秒');
    }

    isPaused.value = false;
    pausedTime.value = null;

    // 重新启动定时器
    console.log('[TouchlessTimeout] 重新启动进度检查定时器');
    startProgressCheck();

    console.log('[TouchlessTimeout] ✅ 超时检测已恢复');
  };

  /**
   * 启动进展检查定时器
   */
  const startProgressCheck = () => {
    // 先清理任何现有的定时器
    if (progressCheckTimer.value) {
      console.log("[TouchlessTimeout] 清理现有定时器");
      clearInterval(progressCheckTimer.value);
      progressCheckTimer.value = null;
    }

    console.log("[TouchlessTimeout] 启动新的进度检查定时器");
    progressCheckTimer.value = setInterval(() => {
      if (!isDetectionActive.value) {
        console.log("[TouchlessTimeout] 检测未激活，停止定时器");
        clearInterval(progressCheckTimer.value);
        progressCheckTimer.value = null;
        return;
      }

      if (isPaused.value) {
        console.log("[TouchlessTimeout] 检测已暂停，跳过检查");
        return;
      }

      const currentTime = Date.now();
      const startTime = actionStartTime.value;

      if (!startTime) {
        console.log("[TouchlessTimeout] 开始时间为空，停止检查");
        return;
      }

      const rawElapsed = currentTime - startTime;
      const elapsed = Math.floor(rawElapsed/1000);
      const rawRemaining = Math.max(0, TIMEOUT_CONFIG.maxAttemptTime - rawElapsed);
      const remaining = Math.floor(rawRemaining/1000);
      console.log(`[TouchlessTimeout] 检查进度 - 已用时: ${elapsed}秒, 剩余: ${remaining}秒`);

      // 检查是否需要发出警告（倒计时）
      const shouldWarnNow = rawElapsed > TIMEOUT_CONFIG.warningTime && !hasWarned.value;
      const shouldTimeoutNow = rawElapsed > TIMEOUT_CONFIG.maxAttemptTime;

      if (shouldWarnNow) {
        handleWarning();
      }
      // 检查是否需要超时处理
      if (shouldTimeoutNow) {
        handleTimeout();
        return; // 超时后立即返回，避免继续执行
      }
    }, TIMEOUT_CONFIG.progressCheckInterval);
  };

  /**
   * 处理警告（倒计时）
   */
  const handleWarning = () => {
    if (hasWarned.value) return;

    hasWarned.value = true;
    const remainingSeconds = countdownSeconds.value;
    console.log(
      `[TouchlessTimeout] 🚨 开始倒计时警告，剩余时间: ${remainingSeconds}秒`
    );
    // 保存回调函数引用
    const onWarningCallback = callbacks.value.onWarning;

    if (onWarningCallback) {
      console.log('[TouchlessTimeout] 🚀 调用警告回调');
      onWarningCallback({
        type: "countdown_warning",
        remainingSeconds,
        totalSeconds: TIMEOUT_CONFIG.maxAttemptTime / 1000,
        message: `动作即将超时，剩余 ${remainingSeconds} 秒`,
      });
      console.log('[TouchlessTimeout] ✅ 警告回调调用完成');
    } 
  };

  /**
   * 处理超时
   */
  const handleTimeout = () => {
    // 保存当前状态用于回调
    const timeoutData = {
      type: "timeout",
      action: currentAction.value,
      elapsedTime: elapsedTime.value,
      reason: "30_second_timeout",
    };
    // 保存回调函数引用（在停止检测前）
    const onTimeoutCallback = callbacks.value.onTimeout;
    // 停止检测
    stopDetection();
    // 触发跳过回调
    if (onTimeoutCallback) {
      onTimeoutCallback(timeoutData);
    } 
  };

  return {
    // 状态
    isDetectionActive,
    isPaused,
    elapsedTime,
    remainingTime,
    countdownSeconds,
    shouldWarn,
    shouldTimeout,

    // 方法
    startDetection,
    stopDetection,
    pauseDetection,
    resumeDetection,
    getDetectionStatus: () => ({
      isActive: isDetectionActive.value,
      isPaused: isPaused.value,
      action: currentAction.value,
      elapsedTime: elapsedTime.value,
      remainingTime: remainingTime.value,
      countdownSeconds: countdownSeconds.value,
      hasWarned: hasWarned.value,
    }),

    // 配置
    TIMEOUT_CONFIG,
  };
}
