<template>
  <!-- 
    使用 v-if="isMounted" 控制 DOM 元素的存在，
    使用 :class="isVisible..." 控制入场/离场动画。
  -->
  <div v-if="isMounted" 
       class="fixed z-[1000] pointer-events-none 
              inset-x-4 top-4 sm:inset-auto sm:top-5 sm:right-5">
    
    <!-- 内容卡片 -->
    <div class="pointer-events-auto flex items-center gap-4 rounded-2xl border border-white/20 
                 p-4 pr-6 text-white shadow-lg shadow-red-600/30
                 bg-gradient-to-br from-amber-500 to-red-600
                 transition-all duration-300 ease-in-out animate-pulse-gentle"
         :class="isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'">
      
      <!-- 倒计时圆环 -->
      <div class="relative h-16 w-16 flex-shrink-0">
        <!-- SVG 容器 -->
        <svg class="h-full w-full -rotate-90" viewBox="0 0 100 100">
          <!-- 背景环 -->
          <circle cx="50" cy="50" r="45" fill="none" stroke="currentColor" stroke-width="5" class="text-white/20"/>
          <!-- 进度环 -->
          <circle 
            cx="50" 
            cy="50" 
            r="45"
            fill="none"
            stroke="currentColor"
            stroke-width="5"
            stroke-linecap="round"
            class="text-white transition-stroke-dashoffset duration-1000 linear"
            :stroke-dasharray="circumference"
            :stroke-dashoffset="progressOffset"
          />
        </svg>
        <!-- 居中的数字 -->
        <div class="absolute inset-0 flex flex-col items-center justify-center">
          <span class="text-2xl font-bold leading-none">{{ remainingSeconds }}</span>
          <span class="text-xs uppercase text-white/70">秒</span>
        </div>
      </div>
      
      <!-- 文本信息 -->
      <div class="flex-1">
        <p class="text-base font-semibold text-shadow">{{ actionName }}</p>
        <p class="text-sm text-white/80">即将自动跳过</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

const props = defineProps({
  warningData: {
    type: Object,
    required: true,
    default: () => ({ remainingSeconds: 10, totalSeconds: 30 })
  },
  actionName: {
    type: String,
    default: '当前动作'
  }
})

const emit = defineEmits(['hide', 'timeout'])

// 响应式数据
const isMounted = ref(false) // 控制 DOM 的挂载
const isVisible = ref(false) // 控制入场/离场动画
const remainingSeconds = ref(props.warningData.remainingSeconds || 10)
let countdownTimer = null

// 计算属性
const circumference = 2 * Math.PI * 45
const progressOffset = computed(() => {
  const totalSeconds = props.warningData.totalSeconds || 30
  if (totalSeconds === 0) return circumference
  // 进度从 0 (满环) 到 1 (空环)
  const progress = remainingSeconds.value / totalSeconds
  return circumference * (1 - progress)
})

// 方法
const startCountdown = () => {
  cleanup() // 先清理旧的计时器，以防万一
  countdownTimer = setInterval(() => {
    if (remainingSeconds.value > 0) {
      remainingSeconds.value--
    } else {
      cleanup()
      emit('timeout')
      hideWarning()
    }
  }, 1000)
}

const hideWarning = () => {
  isVisible.value = false // 触发离场动画
  emit('hide')
  
  // 等待离场动画结束后再销毁组件
  setTimeout(() => {
    isMounted.value = false
  }, 400) // 时间应略长于 CSS transition duration
}

const cleanup = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 监听 props 变化，重置倒计时
watch(() => props.warningData, (newData) => {
  remainingSeconds.value = newData.remainingSeconds || 10
  startCountdown()
}, { deep: true, immediate: false }) // 不在初始时触发

// 生命周期
onMounted(() => {
  isMounted.value = true
  
  nextTick(() => {
    isVisible.value = true
    startCountdown()
  })
})

onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
/* 自定义动画和工具类 */
.text-shadow {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.transition-stroke-dashoffset {
  transition-property: stroke-dashoffset;
}

@keyframes pulse-gentle {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 10px 15px -3px rgba(220, 38, 38, 0.3), 0 4px 6px -4px rgba(220, 38, 38, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(220, 38, 38, 0.4), 0 8px 10px -6px rgba(220, 38, 38, 0.4);
  }
}

.animate-pulse-gentle {
  animation: pulse-gentle 2.5s infinite ease-in-out;
}
</style>