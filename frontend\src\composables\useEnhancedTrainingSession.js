/**
 * 增强训练会话管理
 * 核心调度器，集成动作评估、状态转换、报告和无接触控制。
 * @returns {object} 训练会话的响应式状态和控制方法
 */
import { ref, computed, watch } from "vue";
import { useActionEvaluationEngine } from "./useActionEvaluationEngine";
import { useTouchlessActionTimeout } from "./useTouchlessActionTimeout";
import { useConnectionStore } from "@/stores/connection";
import { useTrainingStore } from "@/stores/training";
import { useWorkflowStore } from "@/stores/workflow";
import { useTrainingReportStore } from "@/stores/trainingReport";
import { usePatientStore } from "@/stores/patient";
import { useStateTransition } from "./useStateTransition";
import { useAudioFeedback } from "./useAudioFeedback";
import { saveToLocalFile } from "@/utils/trainingDataManager";

// 常量定义，便于管理
const ACTION_SKIP_NOTICE_DURATION = 4000; // 与UI组件的显示时间保持一致

export function useEnhancedTrainingSession() {
  // 依赖注入
  const connectionStore = useConnectionStore();
  const trainingStore = useTrainingStore();
  const workflowStore = useWorkflowStore();
  const trainingReportStore = useTrainingReportStore();
  const patientStore = usePatientStore();
  const stateTransition = useStateTransition();
  const evaluationEngine = useActionEvaluationEngine();
  const audioFeedback = useAudioFeedback();
  const touchlessTimeout = useTouchlessActionTimeout();

  // 会话状态
  const isSessionActive = ref(false);
  const sessionStartTime = ref(null);
  const currentActionStartTime = ref(null);

  // --- 私有辅助函数 ---

  /**
   * 启动超时检测。封装了启动逻辑，确保先停止旧的检测。
   * @param {object} action - 当前动作对象
   */
  const _startTimeoutDetection = (action) => {
    if (!action) return;
    console.log(`[Session] 🚀 启动超时检测: ${action.action_name}`);
    // 确保先停止任何可能正在运行的检测
    touchlessTimeout.stopDetection();
    touchlessTimeout.startDetection(action, {
      onTimeout: _handleActionTimeout,
      onWarning: (warningData) => {
        console.log("[Session] 🚨 倒计时警告:", warningData);
        window.trainingViewInstance?.showCountdownWarningDialog(warningData);
      },
    });
  };

  /**
   * 处理动作超时事件
   * @param {object} timeoutData - 超时数据
   */
  const _handleActionTimeout = (timeoutData) => {
    evaluationEngine.stopEvaluation();
    trainingReportStore.completeActionRecord(0, "timeout_skipped");

    // 显示跳过通知UI
    if (window.trainingViewInstance?.showActionSkipDialog) {
      const skipData = {
        actionName: trainingStore.currentAction?.action_name || "当前动作",
        reason: "timeout",
        message: "动作超时，自动跳过",
        duration: ACTION_SKIP_NOTICE_DURATION,
        ...timeoutData,
      };
      window.trainingViewInstance.showActionSkipDialog(skipData);
    } else {
      console.warn("[Session] TrainingView实例不可用，无法显示跳过通知");
    }

    // 使用常量延迟，确保与UI同步
    setTimeout(_proceedToNextState, ACTION_SKIP_NOTICE_DURATION);
  };

  /**
   * 处理动作完成事件
   */
  const _handleActionComplete = () => {
    if (!evaluationEngine.isCompleted.value) return;

    touchlessTimeout.stopDetection();
    const finalScore = evaluationEngine.currentScore.value;
    trainingReportStore.completeActionRecord(finalScore, "completed");
    trainingStore.completeCurrentAction(finalScore);
    evaluationEngine.stopEvaluation();
    stateTransition.handleActionComplete(finalScore);
  };

  /**
   * 决定下一个状态（准备下一个动作或结束报告）
   */
  const _proceedToNextState = () => {
    if (trainingStore.isLastAction()) {
      workflowStore.transitionToReporting();
      endSession();
    } else {
      trainingStore.moveToNextAction();
      workflowStore.transitionToNextActionPreparation();
    }
  };

  /**
   * 安全地保存报告文件
   * @param {object} reportData - 报告数据
   */
  const _saveReport = async (reportData) => {
    if (!reportData) return;
    try {
      console.log("[Session] 开始保存训练数据...");
      const patientId = patientStore.userInfo?.patient_id ?? "default_patient";
      const result = await saveToLocalFile(reportData, patientId);
      if (result.success) {
        console.log(`[Session] 训练数据保存成功: ${result.path}`);
      } 
    } catch (error) {
      console.error("[Session] 保存训练数据时发生严重错误:", error);
    }
  };

  /**
   * 加载当前动作的评估器，并启动相关流程
   */
  const loadCurrentActionDetector = () => {
    const action = trainingStore.currentAction;
    if (!action) {
      console.error("[Session] 无法加载检测器：无当前动作。");
      return false;
    }

    console.log(`[Session] 加载动作: ${action.action_name}`);
    currentActionStartTime.value = Date.now();

    const actionConfig = {
      // action_id: action.action_id,
      action_type: action.action_type,
      action_name: action.action_name,
      side: action.side ?? "left",
      difficulty_level: action.difficulty_level ?? "medium",
    };

    trainingReportStore.startActionRecord(actionConfig);
    const success = evaluationEngine.loadDetector(
      actionConfig.action_type,
      actionConfig.side,
      actionConfig.difficulty_level
    );
    if (success) {
      // 设置当前动作到音频系统，用于智能状态管理
      audioFeedback.setCurrentAction(actionConfig);
      // 播报动作指导
      audioFeedback.announceAction(actionConfig.action_type, actionConfig.side);
      if (workflowStore.currentState === "training") {
        _startTimeoutDetection(action);
      }
    } else {
      console.error("[Session] ❌ 检测器加载失败:", actionConfig);
    }

    return success;
  };

  /**
   * 启动训练会话
   */
  const startSession = () => {
    if (!trainingStore.currentAction) {
      console.warn("[Session] 无法启动会话：训练列表为空。");
      return false;
    }

    console.log("[Session] ✅ 训练会话启动");
    isSessionActive.value = true;
    sessionStartTime.value = Date.now();

    // 播放会话开始音频
    audioFeedback.speak('session_start');

    const patientInfo = {
      patient_id: patientStore.userInfo?.patient_id ?? "default_patient",
      patient_name: patientStore.userInfo?.patient_name ?? "默认患者",
    };
    trainingReportStore.startSessionReport(
      patientInfo,
      `session_${Date.now()}`
    );
    loadCurrentActionDetector();

    return true;
  };

  /**
   * 结束训练会话
   */
  const endSession = async () => {
    if (!isSessionActive.value) return;
    console.log("[Session] 🏁 训练会话结束");
    isSessionActive.value = false;

    evaluationEngine.stopEvaluation();
    touchlessTimeout.stopDetection();

    // 播放会话结束音频
    audioFeedback.speak('session_end');

    const detailedReport = trainingReportStore.completeSessionReport();
    console.log("[Session] 详细训练报告已生成。");

    await _saveReport(detailedReport);

    return { detailedReport };
  };

  /**
   * 暂停训练会话
   */
  const pauseTrainingSession = (reason = "manual_pause") => {
    if (workflowStore.isPaused) return;
    console.log(`[Session] 🛑 暂停训练，原因: ${reason}`);

    // 播放暂停音频
    audioFeedback.speak('session_pause');
    workflowStore.pauseWorkflow(reason);

    // 暂停超时检测
    const timeoutStatus = touchlessTimeout.getDetectionStatus();
    console.log("[Session] 暂停前超时检测状态:", timeoutStatus);
    touchlessTimeout.pauseDetection();
    console.log("[Session] 超时检测已暂停");

    evaluationEngine.pauseEvaluation();
  };

  /**
   * 恢复训练会话
   */
  const resumeTrainingSession = () => {
    if (!workflowStore.isPaused) return;
    console.log("[Session] ▶️ 恢复训练");

    // 播放恢复音频
    audioFeedback.speak('session_resume');

    workflowStore.resumeWorkflow();

    // 检查超时检测状态并恢复
    const timeoutStatus = touchlessTimeout.getDetectionStatus();
    console.log("[Session] 超时检测状态:", timeoutStatus);

    if (touchlessTimeout.isDetectionActive.value) {
      console.log("[Session] 恢复超时检测");
      touchlessTimeout.resumeDetection();
    } else if (trainingStore.currentAction) {
      console.log("[Session] 重新启动超时检测");
      _startTimeoutDetection(trainingStore.currentAction);
    } else {
      console.warn("[Session] 无法恢复超时检测：没有当前动作");
    }

    evaluationEngine.resumeEvaluation();
  };

  /**
   * 切换暂停/恢复状态
   */
  const togglePause = () => {
    workflowStore.isPaused ? resumeTrainingSession() : pauseTrainingSession();
  };

  /**
   * 重新开始当前动作
   */
  const restartCurrentAction = () => {
    if (!trainingStore.currentAction) return false;
    console.log("[Session] 🔄 重新开始当前动作");

    // 停止所有相关进程
    evaluationEngine.stopEvaluation();
    touchlessTimeout.stopDetection();

    // 清空当前动作的音频播放记录，允许重新播放指导音频
    audioFeedback.clearCurrentActionPlayed();

    // 重新加载检测器
    loadCurrentActionDetector();
    return true;
  };

  /**
   * 继续到下一个动作（用于UI触发）
   */
  const proceedToNextAction = () => {
    console.log("[Session] 手动触发，进入下一个状态");

    // 清空当前动作的音频播放记录
    audioFeedback.clearCurrentActionPlayed();

    _proceedToNextState();
  };

  // --- Watchers ---

  // 监听姿态数据，驱动评估引擎
  watch(
    () => connectionStore.poseKeypoints,
    (newKeypoints) => {
      if (!isSessionActive.value || workflowStore.isPaused || !newKeypoints)
        return;
      const result = evaluationEngine.updateEvaluation(newKeypoints);
      if (result?.state) {
        audioFeedback.provideFeedback(
          result.state,
          result.score,
          result.feedback
        );
      }
      if (evaluationEngine.isCompleted.value) {
        _handleActionComplete();
      }
    },
    { deep: true }
  );

  // 监听工作流状态变化，自动管理会话
  watch(
    () => workflowStore.currentState,
    (newState, oldState) => {
      console.log(`[Session] 工作流: ${oldState} -> ${newState}`);
      if (newState === "training") {
        if (!isSessionActive.value) {
          startSession();
        } else if (
          oldState === "preparation" ||
          !touchlessTimeout.isDetectionActive.value
        ) {
          // 从准备状态进入，或任何状态进入但超时检测未运行，则加载/重启检测
          loadCurrentActionDetector();
        }
      } else if (isSessionActive.value && oldState === "training") {
        // 离开训练状态时，停止超时检测以节省资源
        touchlessTimeout.stopDetection();
      }
    }
  );

  // 监听动作变化，自动加载新评估器
  watch(
    () => trainingStore.currentAction,
    (newAction, oldAction) => {
      if (
        isSessionActive.value &&
        workflowStore.currentState === "training" &&
        newAction &&
        newAction.action_id !== oldAction?.action_id
      ) {
        console.log(
          `[Session] 动作切换: ${oldAction?.action_name ?? "无"} -> ${
            newAction.action_name
          }`
        );
        loadCurrentActionDetector();
      }
    },
    { deep: true }
  );

  // --- Computed Properties ---

  const sessionDuration = computed(() =>
    sessionStartTime.value
      ? Math.floor((Date.now() - sessionStartTime.value) / 1000)
      : 0
  );
  const currentActionDuration = computed(() =>
    currentActionStartTime.value
      ? Math.floor((Date.now() - currentActionStartTime.value) / 1000)
      : 0
  );
  const currentActionStage = computed(() => {
    const stageMap = {
      COMPLETED: "completed",
      RETURNING: "returning",
      HOLDING: "holding",
      MOVING_TO_TARGET: "moving",
      IDLE: "waiting",
      ERROR: "error",
    };
    return stageMap[evaluationEngine.currentState.value] ?? "waiting";
  });

  return {
    isSessionActive,
    sessionDuration,
    currentActionDuration,
    currentScore: evaluationEngine.currentScore,
    currentFeedback: evaluationEngine.currentFeedback,
    currentState: evaluationEngine.currentState,
    currentActionStage,
    isActionCompleted: evaluationEngine.isCompleted,

    startSession,
    endSession,
    togglePause,
    restartCurrentAction,
    proceedToNextAction,
    pauseTrainingSession,
    resumeTrainingSession,

    audioEnabled: audioFeedback.isEnabled,
    audioVolume: audioFeedback.volume,
    toggleAudio: audioFeedback.toggleAudio,
    setAudioVolume: audioFeedback.setVolume,

    // 暴露内部函数用于特殊情况，但不鼓励常规使用
    _internal: {
      loadCurrentActionDetector,
      stopTimeoutDetection: touchlessTimeout.stopDetection,
      resetCurrentDetector: evaluationEngine.resetDetector,
    },
  };
}
