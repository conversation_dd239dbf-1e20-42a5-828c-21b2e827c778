<template>
  <!-- 主容器：使用 v-if 和 v-show 结合，实现平滑的离场动画 -->
  <div v-if="isMounted" 
       class="fixed inset-0 z-[1000] flex items-center justify-center p-4 transition-opacity duration-300"
       :class="isVisible ? 'opacity-100' : 'opacity-0'">
    
    <!-- 背景遮罩：柔和的毛玻璃效果 -->
    <div class="absolute inset-0 bg-slate-900/20 backdrop-blur-sm"></div>
    
    <!-- 内容卡片：居中，带有入场动画 -->
    <div v-if="isVisible" class="relative w-full max-w-md transform rounded-3xl border border-white/10 bg-gradient-to-br from-green-600 to-emerald-700 p-8 text-center text-white shadow-2xl shadow-emerald-900/40 transition-all duration-400 ease-out"
         :class="isVisible ? 'translate-y-0 opacity-100 scale-100' : '-translate-y-10 opacity-0 scale-95'">
      
      <!-- 倒计时图标：主视觉中心 -->
      <div class="relative mx-auto mb-6 h-28 w-28">
        <!-- SVG 进度环 -->
        <svg class="absolute inset-0 -rotate-90" viewBox="0 0 100 100">
          <!-- 背景环 -->
          <circle class="text-white/20" cx="50" cy="50" r="45" fill="none" stroke="currentColor" stroke-width="6"/>
          <!-- 进度环 -->
          <circle 
            class="text-white transition-all duration-1000 ease-linear"
            cx="50" 
            cy="50" 
            r="45"
            fill="none"
            stroke="currentColor"
            stroke-width="6"
            stroke-linecap="round"
            :stroke-dasharray="circumference"
            :stroke-dashoffset="progressOffset"
          />
        </svg>
        <!-- 中间的 √ 图标，带有缩放动画 -->
        <div class="flex h-full w-full items-center justify-center rounded-full bg-white/20"
             :class="iconVisible ? 'scale-100 opacity-100' : 'scale-0 opacity-0'"
             style="transition: transform 0.4s ease-out, opacity 0.4s ease-out;">
          <svg class="h-12 w-12" fill="none" viewBox="0 0 24 24">
            <path d="M4.5 12.75l6 6 9-13.5" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <!-- 倒计时数字 -->
        <div class="absolute inset-0 flex items-center justify-center text-xl font-bold">
          {{ countdown }}
        </div>
      </div>
      
      <!-- 信息内容 -->
      <div class="flex flex-col items-center">
        <!-- 主标题：动作名称 -->
        <h3 class="text-2xl font-bold text-shadow">{{ skipData.action?.action_name || '未知动作' }}</h3>
        <!-- 副标题：状态 -->
        <p class="mt-1 text-lg font-medium text-white/90">已跳过</p>
        
        <!-- 跳过原因 -->
        <p class="mt-4 rounded-full bg-black/20 px-4 py-1.5 text-sm text-white/80">
          原因: {{ getSkipReasonText(skipData.reason) }}
        </p>

        <!-- 详细信息（可选） -->
        <div v-if="showDetails && (skipData.elapsedTime || skipData.stagnationTime)" 
             class="mt-6 w-full space-y-2 rounded-xl bg-white/10 p-4 text-left text-sm">
          <div v-if="skipData.elapsedTime" class="flex justify-between">
            <span class="text-white/70">尝试时长:</span>
            <span class="font-semibold">{{ formatDuration(skipData.elapsedTime) }}</span>
          </div>
          <div v-if="skipData.stagnationTime" class="flex justify-between">
            <span class="text-white/70">停滞时长:</span>
            <span class="font-semibold">{{ formatDuration(skipData.stagnationTime) }}</span>
          </div>
        </div>

        <!-- 下一步提示 -->
        <div class="mt-6 flex items-center space-x-2 text-sm text-white/80">
          <span>{{ getNextActionText() }}</span>
          <!-- 加载点动画 -->
          <div class="flex space-x-1">
            <span class="h-1.5 w-1.5 animate-dot-pulse rounded-full bg-white [animation-delay:-0.3s]"></span>
            <span class="h-1.5 w-1.5 animate-dot-pulse rounded-full bg-white [animation-delay:-0.15s]"></span>
            <span class="h-1.5 w-1.5 animate-dot-pulse rounded-full bg-white"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
  skipData: { type: Object, required: true, default: () => ({ action: { action_name: '未知动作' }, reason: 'timeout', elapsedTime: 0, stagnationTime: 0 }) },
  displayDuration: { type: Number, default: 4000 },
  showDetails: { type: Boolean, default: true }
})

const emit = defineEmits(['hide', 'complete'])

// 响应式数据
const isMounted = ref(false) // 用于控制 DOM 挂载
const isVisible = ref(false) // 用于控制入场和离场动画
const iconVisible = ref(false) // 控制 √ 图标的动画
const countdown = ref(Math.ceil(props.displayDuration / 1000))
let countdownTimer = null
let hideTimer = null

// 计算属性
const circumference = 2 * Math.PI * 45
const progressOffset = computed(() => {
  const totalSeconds = Math.ceil(props.displayDuration / 1000)
  if (totalSeconds === 0) return 0
  const progress = (countdown.value) / totalSeconds
  // 从满环开始，逐渐变空
  return circumference * (1 - progress)
})

// 方法
const getSkipReasonText = (reason) => { /* 逻辑不变 */ 
  const reasonMap = { 'timeout': '动作执行超时', 'stagnation': '动作进展停滞', 'manual': '手动跳过', 'repeated_failure': '多次尝试失败', 'emergency_stop': '紧急停止', 'identity_lost': '身份验证失败' };
  return reasonMap[reason] || '系统自动跳过';
}

const formatDuration = (milliseconds) => { /* 逻辑不变 */ 
  if (!milliseconds && milliseconds !== 0) return '0秒';
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  if (minutes > 0) return `${minutes}分 ${remainingSeconds}秒`;
  return `${seconds}秒`;
}

const getNextActionText = () => { /* 逻辑不变 */ 
  if (props.skipData.isLastAction) return '正在生成训练报告...';
  return '正在准备下一个动作...';
}

const startTimers = () => {
  countdownTimer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      clearInterval(countdownTimer)
    }
  }, 1000)

  hideTimer = setTimeout(hideNotice, props.displayDuration)
}

const hideNotice = () => {
  isVisible.value = false // 触发离场动画
  emit('hide')
  
  // 等待离场动画结束后再销毁组件和触发 complete 事件
  setTimeout(() => {
    isMounted.value = false
    emit('complete')
  }, 400) // 这个时间应与 CSS transition duration 匹配
}

const cleanup = () => {
  clearInterval(countdownTimer)
  clearTimeout(hideTimer)
}

// 生命周期
onMounted(() => {
  isMounted.value = true
  
  // 使用 nextTick 确保 DOM 元素已经渲染，然后再触发入场动画
  nextTick(() => {
    isVisible.value = true
    setTimeout(() => { iconVisible.value = true }, 200) // 延迟图标动画
    startTimers()
  })
})

onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
/* 自定义 text-shadow 工具类 */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 
  加载点动画可以直接在 tailwind.config.js 中配置，
  但为了组件独立性，这里直接定义。
*/
@keyframes dot-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}
.animate-dot-pulse {
  animation: dot-pulse 1.4s infinite ease-in-out;
}
</style>