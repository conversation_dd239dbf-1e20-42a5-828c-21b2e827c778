<template>
  <!-- 主容器：固定定位，覆盖全屏，居中显示 -->
  <div v-if="isVisible" class="fixed inset-0 z-[1500] flex items-center justify-center p-4 animate-fade-in">
    <!-- 背景遮罩：红色半透明 + 毛玻璃效果 + 呼吸动画 -->
    <div class="absolute inset-0 bg-red-700/30 backdrop-blur-md animate-pulse-bg"></div>
    <!-- 内容卡片：相对定位，渐变背景，圆角，阴影，边框，脉冲动画 -->
    <div class="relative flex w-full max-w-lg flex-col items-center rounded-3xl border-2 border-white/20 bg-gradient-to-br from-red-600 to-red-800 p-6 text-center text-white shadow-2xl shadow-red-900/50 sm:p-10 animate-card-pulse">
      <!-- 紧急图标：圆形背景，抖动动画 -->
      <div class="mb-6 flex h-24 w-24 items-center justify-center rounded-full bg-white/20 animate-wiggle">
        <svg class="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"/>
        </svg>
      </div>
      <!-- 紧急信息 -->
      <div class="flex w-full flex-col items-center">
        <!-- 标题：大字号，粗体，文本阴影 -->
        <h2 class="text-3xl font-extrabold text-shadow sm:text-4xl">{{ getEmergencyTitle() }}</h2>
        <!-- 状态指示器：与标题结合，更紧凑 -->
        <div class="mt-4 flex items-center">
          <div class="h-3 w-3 rounded-full animate-pulse" :class="getStatusClass()"></div>
          <span class="ml-2 text-sm font-medium uppercase tracking-wider text-white/90">{{ getStatusText() }}</span>
        </div>
        
        <!-- 消息文本 -->
        <p class="mt-4 max-w-md text-base text-white/90 sm:text-lg">{{ getEmergencyMessage() }}</p>
        
        <!-- 详情卡片（可选） -->
        <div v-if="showDetails" class="mt-8 grid w-full gap-3 text-left">
          <div class="rounded-xl bg-white/10 p-4">
            <div class="flex items-center">
              <span class="mr-3 text-xl">⚠️</span>
              <div class="flex-1">
                <p class="text-sm font-semibold text-white/80">异常类型</p>
                <p class="font-medium">{{ getExceptionTypeText() }}</p>
              </div>
            </div>
          </div>
          <div v-if="emergencyData.gesture" class="rounded-xl bg-white/10 p-4">
             <div class="flex items-center">
              <span class="mr-3 text-xl">👋</span>
              <div class="flex-1">
                <p class="text-sm font-semibold text-white/80">检测到手势</p>
                <p class="font-medium">{{ emergencyData.gesture.config?.name || '未知手势' }}</p>
              </div>
            </div>
          </div>
          <div v-if="emergencyData.action" class="rounded-xl bg-white/10 p-4">
            <div class="flex items-center">
              <span class="mr-3 text-xl">🎯</span>
              <div class="flex-1">
                <p class="text-sm font-semibold text-white/80">当前动作</p>
                <p class="font-medium">{{ emergencyData.action.action_name || '无' }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 自动恢复倒计时 -->
        <div v-if="autoRecovery && countdown > 0" class="mt-8 w-full rounded-xl bg-white/10 p-4">
          <p class="text-sm font-medium">系统将在 <span class="font-bold">{{ countdown }}</span> 秒后自动恢复</p>
          <div class="mt-2 h-2 w-full overflow-hidden rounded-full bg-white/20">
            <div class="h-full rounded-full bg-gradient-to-r from-green-400 to-emerald-500 transition-all duration-1000 ease-linear" :style="{ width: progressPercentage + '%' }"></div>
          </div>
        </div>

        <!-- 身份验证异常倒计时（特殊样式） -->
        <div v-if="emergencyData.type === 'identity_exception' && countdown > 0" class="mt-8 w-full rounded-xl border border-amber-500/30 bg-amber-500/10 p-4">
          <p class="text-sm font-medium text-amber-300">{{ countdown }} 秒后未返回画面将终止训练</p>
          <div class="mt-2 h-2 w-full overflow-hidden rounded-full bg-amber-500/20">
            <div class="h-full rounded-full bg-amber-400 transition-all duration-1000 ease-linear" :style="{ width: progressPercentage + '%' }"></div>
          </div>
        </div>
        
        <!-- 操作提示 -->
        <div class="mt-8 grid w-full gap-2 text-sm text-white/70">
          <div v-for="hint in getActionHints()" :key="hint.id" class="flex items-center justify-center">
            <span class="mr-2">{{ hint.icon }}</span>
            <span>{{ hint.text }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右上角状态指示器 -->
    <div class="absolute top-6 right-6 flex flex-col items-end gap-2">
      <div v-for="indicator in statusIndicators" :key="indicator.type" class="flex items-center rounded-full bg-black/40 px-3 py-1.5 backdrop-blur-sm">
        <div class="h-2 w-2 animate-pulse rounded-full" :class="indicator.class"></div>
        <span class="ml-2 text-xs font-semibold text-white">{{ indicator.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Props 和 Emits 保持不变
const props = defineProps({
  emergencyData: {
    type: Object,
    required: true,
    default: () => ({
      type: 'emergency_stop',
      reason: 'gesture_detected',
      timestamp: Date.now()
    })
  },
  autoRecovery: { type: Boolean, default: false },
  recoveryDelay: { type: Number, default: 10000 },
  showDetails: { type: Boolean, default: true },
  showManualActions: { type: Boolean, default: true }
})
const emit = defineEmits(['hide', 'recovery', 'manual-action'])

// 响应式数据保持不变
const isVisible = ref(true)
const countdown = ref(Math.ceil(props.recoveryDelay / 1000))
const countdownTimer = ref(null)

// 计算属性
const progressPercentage = computed(() => {
  if (!props.autoRecovery && props.emergencyData.type !== 'identity_exception') return 0;
  const totalSeconds = Math.ceil(props.recoveryDelay / 1000);
  // 确保 totalSeconds 不为 0，避免除以零的错误
  if (totalSeconds === 0) return 100;
  return ((totalSeconds - countdown.value) / totalSeconds) * 100;
});


const statusIndicators = computed(() => {
  const indicators = []
  
  indicators.push({
    type: 'emergency',
    class: 'bg-red-500', // 使用 Tailwind 类
    label: '紧急状态'
  })
  
  switch (props.emergencyData.type) {
    case 'identity_exception':
      indicators.push({
        type: 'identity',
        class: 'bg-purple-500', // 使用 Tailwind 类
        label: '身份异常'
      })
      break
    case 'action_timeout':
      indicators.push({
        type: 'timeout',
        class: 'bg-orange-500', // 使用 Tailwind 类
        label: '动作超时'
      })
      break
  }
  
  return indicators
})

// 方法（主要更新 getStatusClass 以返回 Tailwind 类）
const getEmergencyTitle = () => { /* 逻辑不变 */ 
  const titleMap = { 'emergency_stop': '紧急停止', 'request_help': '请求帮助', 'restart_training': '重新开始', 'identity_exception': '身份验证异常', 'action_timeout': '动作超时', 'system_error': '系统错误' };
  return titleMap[props.emergencyData.type] || '训练暂停';
}

const getEmergencyMessage = () => { /* 逻辑不变 */ 
  const messageMap = { 'emergency_stop': '检测到停止手势，训练已暂停', 'request_help': '检测到求助手势，等待协助', 'restart_training': '检测到重新开始手势，准备重启', 'identity_exception': getIdentityExceptionMessage(), 'action_timeout': '动作执行超时，训练已暂停', 'system_error': '系统出现错误，训练已暂停' };
  return messageMap[props.emergencyData.type] || '训练已暂停，请等待处理';
}

const getIdentityExceptionMessage = () => { /* 逻辑不变 */ 
  const reason = props.emergencyData.reason;
  const reasonMap = { 'user_missing': '未检测到用户，训练已暂停。请回到摄像头前继续训练。', 'unauthorized_user': '检测到其他用户，训练已暂停。请确保只有登录用户在进行训练。', 'identity_lost': '身份验证丢失，训练已暂停', 'unauthorized_person': '检测到未授权人员，训练已暂停', 'multiple_persons': '检测到多人，训练已暂停' };
  return reasonMap[reason] || '身份验证出现异常，请重新验证';
}

const getExceptionTypeText = () => { /* 逻辑不变 */ 
  const typeMap = { 'emergency_stop': '手势控制停止', 'request_help': '用户求助', 'restart_training': '重新开始请求', 'identity_exception': '身份验证失败', 'action_timeout': '动作执行超时', 'system_error': '系统内部错误' };
  return typeMap[props.emergencyData.type] || '未知异常';
}

const getStatusClass = () => {
  // 返回 Tailwind 背景色类
  const classMap = {
    'emergency_stop': 'bg-red-400',
    'request_help': 'bg-amber-400',
    'restart_training': 'bg-blue-400',
    'identity_exception': 'bg-amber-400',
    'action_timeout': 'bg-red-400',
    'system_error': 'bg-red-400'
  }
  return classMap[props.emergencyData.type] || 'bg-red-400'
}

const getStatusText = () => { /* 逻辑不变 */ 
  const statusMap = { 'emergency_stop': '已停止', 'request_help': '等待帮助', 'restart_training': '准备重启', 'identity_exception': '验证中', 'action_timeout': '已超时', 'system_error': '错误状态' };
  return statusMap[props.emergencyData.type] || '暂停中';
}

const getActionHints = () => { /* 逻辑不变 */ 
  const hintsMap = { 'emergency_stop': [{ id: 1, icon: '✋', text: '举起双手继续暂停' }, { id: 2, icon: '✅', text: '放下双手或保持常规姿势以恢复' }], 'request_help': [{ id: 1, icon: '🆘', text: '等待工作人员协助' }, { id: 2, icon: '👋', text: '挥手表示需要帮助' }], 'restart_training': [{ id: 1, icon: '🔄', text: '系统正在重新初始化' }, { id: 2, icon: '⏳', text: '请稍等片刻' }], 'identity_exception': [{ id: 1, icon: '👤', text: '请确保只有患者在镜头前' }, { id: 2, icon: '📷', text: '保持面部清晰可见' }], 'action_timeout': [{ id: 1, icon: '⏰', text: '动作执行时间过长' }, { id: 2, icon: '🎯', text: '系统将自动跳过此动作' }] };
  return hintsMap[props.emergencyData.type] || [{ id: 1, icon: '⏸️', text: '训练已暂停' }, { id: 2, icon: '🔄', text: '等待系统处理' }];
}

// 倒计时和生命周期钩子逻辑保持不变
const startCountdown = () => {
  const shouldShowCountdown = props.autoRecovery || props.emergencyData.type === 'identity_exception'
  if (!shouldShowCountdown) return
  countdownTimer.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      clearInterval(countdownTimer.value)
      if (props.autoRecovery) handleAutoRecovery()
    }
  }, 1000)
}

const handleAutoRecovery = () => {
  emit('recovery', { type: 'auto_recovery', emergencyType: props.emergencyData.type })
  hideEmergency()
}

const hideEmergency = () => {
  isVisible.value = false
  emit('hide')
}

const cleanup = () => {
  if (countdownTimer.value) clearInterval(countdownTimer.value)
}

onMounted(() => {
  if (props.autoRecovery || props.emergencyData.type === 'identity_exception') {
    startCountdown()
  }
})

onUnmounted(() => {
  cleanup()
})
</script>

<!-- 自定义动画和样式 -->
<style scoped>
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  @keyframes pulse-bg {
    0%, 100% { background-color: rgba(220, 38, 38, 0.3); }
    50% { background-color: rgba(220, 38, 38, 0.4); }
  }
  @keyframes card-pulse {
    0%, 100% { transform: scale(1); box-shadow: 0 25px 50px -12px rgba(127, 29, 29, 0.5); }
    50% { transform: scale(1.02); box-shadow: 0 30px 60px -15px rgba(127, 29, 29, 0.6); }
  }
  @keyframes wiggle {
    0%, 100% { transform: rotate(-3deg); }
    50% { transform: rotate(3deg); }
  }

  .animate-fade-in { animation: fade-in 0.3s ease-out; }
  .animate-pulse-bg { animation: pulse-bg 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
  .animate-card-pulse { animation: card-pulse 2s ease-in-out infinite; }
  .animate-wiggle { animation: wiggle 0.5s ease-in-out infinite; }
</style>