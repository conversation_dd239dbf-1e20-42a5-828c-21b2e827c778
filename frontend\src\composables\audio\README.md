# 优化音频反馈系统

## 概述

这是一个全新设计的模块化音频反馈系统，专为智能康复训练平台优化。系统采用智能调度、优先级队列和精确状态管理，提供更流畅、更智能的音频体验。

## 核心特性

### 🎯 智能调度
- **优先级队列**: 系统音频 > 动作指导 > 详细指导 > 状态反馈 > 鼓励语音
- **智能冷却**: 根据音频类型动态调整冷却时间
- **状态机驱动**: 基于训练阶段的精确音频触发

### 🔄 模块化架构
- **AudioConfigManager**: 音频文件配置和优先级规则管理
- **AudioPriorityQueue**: 智能队列管理和播放控制
- **AudioStateManager**: 训练状态跟踪和音频触发决策
- **AudioScheduler**: 核心调度器，协调各模块工作

### 🎵 增强功能
- **渐进式指导**: 从基础指导到详细纠错的分层反馈
- **每阶段单次播放**: 避免重复干扰，确保关键信息传达
- **动作级别的播放控制**: 每个动作的指导音频只播放一次

## 使用方法

### 基础使用

```javascript
import { useAudioFeedback } from '@/composables/useAudioFeedback'

const audioFeedback = useAudioFeedback()

// 激活音频系统（需要用户交互）
await audioFeedback.activateAudioContext()

// 预加载音频文件
audioFeedback.preloadAudioFiles()

// 播放系统音频
audioFeedback.speak('session_start')

// 播报动作指导
audioFeedback.announceAction('shoulder_touch', 'left')

// 提供状态反馈
audioFeedback.provideFeedback('HOLDING', 75, '保持动作')

// 播放音效
audioFeedback.playSound('success')
```

### 训练会话集成

```javascript
// 设置当前动作（用于智能状态管理）
audioFeedback.setCurrentAction({
  action_type: 'shoulder_touch',
  side: 'left',
  action_id: 'action_001',
  action_name: '左侧触肩'
})

// 动作切换时清空播放记录
audioFeedback.clearCurrentActionPlayed()

// 获取系统状态
const status = audioFeedback.getStatus()
console.log('音频系统状态:', status)
```

## 音频文件结构

```
/resources/audio/
├── system/                 # 系统音频（最高优先级）
│   ├── session_start.mp3
│   ├── session_pause.mp3
│   └── session_end.mp3
├── guidance/              # 动作指导（高优先级）
│   ├── shoulder_touch/
│   ├── arm_raise/
│   └── finger_touch/
├── detailed_guidance/     # 详细指导（中高优先级）
│   ├── shoulder_touch/
│   ├── arm_raise/
│   └── general/
└── feedback/             # 状态反馈（中等优先级）
    ├── states/
    ├── completion/
    └── encouragement/
```

## 状态映射

### 训练状态音频映射
- `IDLE` → `prepare` (当反馈包含"准备"时)
- `HOLDING/TOUCHING` → `hold` (每个动作只播放一次)
- `RETURNING/LOWERING` → `return` (每个动作只播放一次)
- `COMPLETED` → `excellent/good/fair` (根据分数) + 成功音效
- `ERROR` → `error` + 错误音效 (3秒特殊冷却)

### 详细指导触发条件
- **肩膀触摸**: 移动阶段分数<30时触发"closer"指导
- **手臂上举**: 抬起阶段分数<40时触发"higher"指导
- **通用指导**: 保持阶段分数<60时触发"stability"指导

### 鼓励语音触发条件
- `keep_going`: 分数30-60，移动阶段
- `almost_there`: 分数60-85，保持阶段
- `good_progress`: 分数>50，返回阶段

## 优先级和冷却规则

| 音频类型 | 优先级 | 冷却时间 | 说明 |
|---------|--------|----------|------|
| 系统音频 | 1 (最高) | 0ms | 无冷却限制 |
| 动作指导 | 2 | 0ms | 每个动作只播放一次 |
| 详细指导 | 3 | 3000ms | 3秒冷却 |
| 状态反馈 | 4 | 1500ms | 1.5秒冷却 |
| 鼓励语音 | 5 (最低) | 5000ms | 5秒冷却 |

## API 参考

### 主要方法

- `speak(audioKey, options)`: 播放语音音频
- `playSound(type)`: 播放音效
- `announceAction(actionType, side)`: 播报动作指导
- `provideFeedback(state, score, feedback)`: 提供状态反馈
- `setCurrentAction(action)`: 设置当前动作
- `clearCurrentActionPlayed()`: 清空当前动作播放记录
- `getStatus()`: 获取系统状态

### 配置方法

- `setVolume(volume)`: 设置音量 (0-1)
- `toggleAudio()`: 切换音频开关
- `activateAudioContext()`: 激活音频上下文
- `preloadAudioFiles()`: 预加载音频文件
- `stopAll()`: 停止所有音频

## 开发和调试

### 测试系统
```javascript
// 在开发环境中运行完整测试
window.runAudioSystemTest()
```

### 状态监控
```javascript
// 获取详细状态信息
const status = audioFeedback.getStatus()
console.log('队列状态:', status.queueStatus)
console.log('状态管理:', status.stateStats)
```

### 调试日志
系统提供详细的控制台日志，包括：
- 音频加载和播放状态
- 队列管理和优先级处理
- 状态变化和触发决策
- 冷却时间和频率控制

## 性能优化

- **音频缓存**: 自动缓存已加载的音频文件
- **智能预加载**: 预加载常用音频文件
- **队列优化**: 高效的优先级队列管理
- **内存管理**: 自动清理过期的播放记录

## 注意事项

1. **用户交互要求**: 音频上下文需要用户交互才能激活
2. **文件路径**: 确保音频文件路径正确且文件存在
3. **浏览器兼容性**: 支持现代浏览器的Web Audio API
4. **性能考虑**: 避免过度频繁的音频播放请求
