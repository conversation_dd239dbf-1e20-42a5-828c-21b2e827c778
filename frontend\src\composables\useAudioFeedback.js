/**
 * 优化的音频反馈系统
 * 模块化设计，智能调度，精确状态管理
 */

import { ref } from 'vue'
import { AudioConfigManager } from './audio/AudioConfigManager'
import { AudioPriorityQueue } from './audio/AudioPriorityQueue'
import { AudioStateManager } from './audio/AudioStateManager'
import { AudioScheduler } from './audio/AudioScheduler'

export function useAudioFeedback() {
  const isEnabled = ref(true)
  const volume = ref(0.7)

  // 初始化模块化组件
  const configManager = new AudioConfigManager()
  const priorityQueue = new AudioPriorityQueue()
  const stateManager = new AudioStateManager()
  const scheduler = new AudioScheduler({
    configManager,
    priorityQueue,
    stateManager,
    isEnabled,
    volume
  })

  console.log('[AudioFeedback] 优化的模块化音频系统已初始化')

  // 响应式状态
  const isPlayingAudio = ref(false)
  const isPlayingSound = ref(false)
  const isAudioContextActivated = ref(false)

  // 监听调度器状态变化
  const updatePlayingStatus = () => {
    const status = scheduler.getStatus()
    isPlayingAudio.value = status.queueStatus.isPlaying
    isAudioContextActivated.value = status.isAudioContextActivated
  }

  // 定期更新状态
  setInterval(updatePlayingStatus, 500)

  /**
   * 预加载常用音频文件
   */
  const preloadAudioFiles = () => {
    scheduler.preloadAudioFiles()
  }

  /**
   * 激活音频上下文
   */
  const activateAudioContext = async () => {
    const result = await scheduler.activateAudioContext()
    updatePlayingStatus()
    return result
  }

  /**
   * 播放音频文件
   * @param {string} audioKey - 音频文件的键名
   * @param {Object} options - 播放选项
   */
  const playAudioFile = async (audioKey, options = {}) => {
    return await scheduler.playAudioFile(audioKey, options)
  }


  /**
   * 语音播报 - 使用智能调度系统
   * @param {string} audioKey - 音频文件的键名
   * @param {Object} options - 播放选项
   */
  const speak = async (audioKey, options = {}) => {
    return await scheduler.scheduleAudio(audioKey, options)
  }

  /**
   * 播放提示音
   * @param {string} type - 音效类型
   */
  const playSound = (type) => {
    scheduler.playSound(type)
  }

  /**
   * 根据训练状态提供音频反馈
   * @param {string} state - 训练状态
   * @param {number} score - 当前分数
   * @param {string} feedback - 反馈文本
   */
  const provideFeedback = (state, score, feedback) => {
    scheduler.provideFeedback(state, score, feedback)
  }

  /**
   * 播报动作指导
   * @param {string} actionType - 动作类型
   * @param {string} side - 侧别
   */
  const announceAction = (actionType, side) => {
    scheduler.announceAction(actionType, side)
  }

  /**
   * 设置音量
   * @param {number} newVolume - 新音量 (0-1)
   */
  const setVolume = (newVolume) => {
    volume.value = Math.max(0, Math.min(1, newVolume))
  }

  /**
   * 切换音频开关
   */
  const toggleAudio = () => {
    isEnabled.value = !isEnabled.value
    if (isEnabled.value) {
      speak('audio_enabled')
    }
  }

  /**
   * 停止所有音频
   */
  const stopAll = () => {
    scheduler.stopAll()
  }

  return {
    // 状态
    isEnabled,
    volume,
    isPlayingAudio,
    isPlayingSound,
    isAudioContextActivated,

    // 方法
    speak,
    playAudioFile,
    playSound,
    provideFeedback,
    announceAction,
    setVolume,
    toggleAudio,
    stopAll,
    activateAudioContext,
    preloadAudioFiles,

    // 新增：获取系统状态
    getStatus: () => scheduler.getStatus(),

    // 新增：设置当前动作（用于状态管理）
    setCurrentAction: (action) => scheduler.stateManager.setCurrentAction(action),

    // 新增：清空当前动作播放记录
    clearCurrentActionPlayed: () => scheduler.priorityQueue.clearCurrentActionPlayed()
  }
}
