/**
 * 音频反馈系统
 * 为训练过程提供本地音频文件播放和音效反馈
 */

import { ref } from 'vue'

export function useAudioFeedback() {
  const isEnabled = ref(true)
  const volume = ref(0.7)
  const audioContext = ref(null)

  // 音频文件路径配置
  const AUDIO_BASE_PATH = '/resources/audio'
  // 音频文件映射 - 完整配置，覆盖所有语音提示
  const AUDIO_FILES = {
    // 系统提示
    system: {
      audio_enabled: `${AUDIO_BASE_PATH}/system/audio_enabled.mp3`,
      session_start: `${AUDIO_BASE_PATH}/system/session_start.mp3`,
      session_pause: `${AUDIO_BASE_PATH}/system/session_pause.mp3`,
      session_resume: `${AUDIO_BASE_PATH}/system/session_resume.mp3`,
      session_end: `${AUDIO_BASE_PATH}/system/session_end.mp3`
    },

    // 动作指导 - 开始提示
    guidance: {
      shoulder_touch_left: `${AUDIO_BASE_PATH}/guidance/shoulder_touch/left_start.mp3`,
      shoulder_touch_right: `${AUDIO_BASE_PATH}/guidance/shoulder_touch/right_start.mp3`,
      arm_raise_left: `${AUDIO_BASE_PATH}/guidance/arm_raise/left_start.mp3`,
      arm_raise_right: `${AUDIO_BASE_PATH}/guidance/arm_raise/right_start.mp3`,
      finger_touch_left: `${AUDIO_BASE_PATH}/guidance/finger_touch/left_start.mp3`,
      finger_touch_right: `${AUDIO_BASE_PATH}/guidance/finger_touch/right_start.mp3`,
      palm_flip_left: `${AUDIO_BASE_PATH}/guidance/palm_flip/left_start.mp3`,
      palm_flip_right: `${AUDIO_BASE_PATH}/guidance/palm_flip/right_start.mp3`
    },

    // 训练状态反馈
    feedback: {
      // 状态提示
      hold: `${AUDIO_BASE_PATH}/feedback/states/hold.mp3`,
      return: `${AUDIO_BASE_PATH}/feedback/states/return.mp3`,
      error: `${AUDIO_BASE_PATH}/feedback/states/error.mp3`,
      prepare: `${AUDIO_BASE_PATH}/feedback/states/prepare.mp3`,

      // 完成度评价
      excellent: `${AUDIO_BASE_PATH}/feedback/completion/excellent.mp3`,
      good: `${AUDIO_BASE_PATH}/feedback/completion/good.mp3`,
      fair: `${AUDIO_BASE_PATH}/feedback/completion/fair.mp3`,

      // 鼓励语音
      keep_going: `${AUDIO_BASE_PATH}/feedback/encouragement/keep_going.mp3`,
      almost_there: `${AUDIO_BASE_PATH}/feedback/encouragement/almost_there.mp3`,
      good_progress: `${AUDIO_BASE_PATH}/feedback/encouragement/good_progress.mp3`
    }
  }

  console.log('[AudioFeedback] 本地音频文件系统已初始化')

  // 音频播放控制
  const lastAudioTime = ref(0)
  const lastAudioKey = ref('')
  const audioCooldown = 2000 // 2秒冷却时间
  const isPlayingAudio = ref(false)

  // 音效播放控制
  const lastSoundTime = ref(0)
  const soundCooldown = 1000 // 1秒音效冷却时间
  const isPlayingSound = ref(false)

  // 音频播放队列管理
  const audioQueue = ref([])
  const isProcessingQueue = ref(false)

  // 音频上下文激活状态
  const isAudioContextActivated = ref(false)

  // 音频元素缓存
  const audioCache = ref(new Map())

  // 初始化音频上下文
  const initAudioContext = () => {
    if (!audioContext.value && window.AudioContext) {
      audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
    }
  }

  /**
   * 获取或创建音频元素
   */
  const getAudioElement = (audioPath) => {
    if (audioCache.value.has(audioPath)) {
      return audioCache.value.get(audioPath)
    }

    const audio = new Audio(audioPath)
    audio.volume = volume.value
    audio.preload = 'auto'

    audioCache.value.set(audioPath, audio)
    return audio
  }

  /**
   * 预加载常用音频文件
   */
  const preloadAudioFiles = () => {
    console.log('[AudioFeedback] 开始预加载音频文件')

    // 预加载系统音频
    Object.values(AUDIO_FILES.system).forEach(path => {
      getAudioElement(path)
    })

    // 预加载反馈音频
    Object.values(AUDIO_FILES.feedback).forEach(path => {
      getAudioElement(path)
    })

    // 预加载指导音频
    Object.values(AUDIO_FILES.guidance).forEach(path => {
      getAudioElement(path)
    })

    console.log('[AudioFeedback] 音频文件预加载完成，缓存数量:', audioCache.value.size)
  }

  /**
   * 激活音频上下文（需要用户交互）
   */
  const activateAudioContext = async () => {
    if (isAudioContextActivated.value) return true
    try {
      initAudioContext()
      if (audioContext.value && audioContext.value.state === 'suspended') {
        await audioContext.value.resume()
      }
      isAudioContextActivated.value = true
      console.log('[AudioFeedback] 音频上下文已激活')
      return true
    } catch (error) {
      console.warn('[AudioFeedback] 音频上下文激活失败:', error)
      return false
    }
  }

  /**
   * 播放音频文件
   * @param {string} audioKey - 音频文件的键名
   * @param {Object} options - 播放选项
   */
  const playAudioFile = async (audioKey, options = {}) => {
    if (!isEnabled.value) {
      console.log('[AudioFeedback] 音频播放被禁用')
      return false
    }

    // 激活音频上下文
    await activateAudioContext()

    // 频率控制
    const currentTime = Date.now()
    if (currentTime - lastAudioTime.value < audioCooldown && lastAudioKey.value === audioKey) {
      console.log('[AudioFeedback] 音频被频率控制跳过:', audioKey)
      return false
    }

    // 查找音频文件路径
    let audioPath = null
    for (const category of Object.values(AUDIO_FILES)) {
      if (category[audioKey]) {
        audioPath = category[audioKey]
        break
      }
    }

    if (!audioPath) {
      console.warn('[AudioFeedback] 未找到音频文件:', audioKey)
      return false
    }

    try {
      console.log('[AudioFeedback] 播放音频文件:', audioKey, audioPath)

      const audio = getAudioElement(audioPath)
      audio.volume = volume.value * (options.volume || 1.0)

      // 更新状态
      lastAudioTime.value = currentTime
      lastAudioKey.value = audioKey
      isPlayingAudio.value = true
      console.log('[AudioFeedback] 音频播放状态设置为:', isPlayingAudio.value)

      // 播放音频
      await audio.play()

      // 监听播放结束
      return new Promise((resolve) => {
        const handleEnded = () => {
          console.log('[AudioFeedback] 音频播放完成:', audioKey)
          isPlayingAudio.value = false
          console.log('[AudioFeedback] 音频播放状态重置为:', isPlayingAudio.value)
          audio.removeEventListener('ended', handleEnded)
          audio.removeEventListener('error', handleError)
          resolve(true)
        }

        const handleError = (error) => {
          console.warn('[AudioFeedback] 音频播放出错:', audioKey, error)
          isPlayingAudio.value = false
          console.log('[AudioFeedback] 音频播放状态因错误重置为:', isPlayingAudio.value)
          audio.removeEventListener('ended', handleEnded)
          audio.removeEventListener('error', handleError)
          resolve(false)
        }

        audio.addEventListener('ended', handleEnded)
        audio.addEventListener('error', handleError)
      })

    } catch (error) {
      console.warn('[AudioFeedback] 音频播放失败:', audioKey, error)
      isPlayingAudio.value = false
      console.log('[AudioFeedback] 音频播放状态因异常重置为:', isPlayingAudio.value)
      return false
    }
  }



  /**
   * 处理音频播放队列
   */
  const processAudioQueue = async () => {
    if (isProcessingQueue.value || audioQueue.value.length === 0) {
      return
    }

    isProcessingQueue.value = true

    while (audioQueue.value.length > 0) {
      const { audioKey, options } = audioQueue.value.shift()
      console.log('[AudioFeedback] 处理队列中的音频:', audioKey)

      await playAudioFile(audioKey, options)

      // 等待一小段时间再处理下一个
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    isProcessingQueue.value = false
  }

  /**
   * 语音播报 - 使用本地音频文件播放
   * @param {string} audioKey - 音频文件的键名
   * @param {Object} options - 播放选项
   */
  const speak = async (audioKey, options = {}) => {
    if (!isEnabled.value) {
      console.log('[AudioFeedback] 音频播放被禁用')
      return false
    }

    // 激活音频上下文
    await activateAudioContext()

    // 频率控制
    const currentTime = Date.now()
    if (currentTime - lastAudioTime.value < audioCooldown && lastAudioKey.value === audioKey) {
      console.log('[AudioFeedback] 音频被频率控制跳过:', audioKey)
      return false
    }
    console.log('[AudioFeedback] 音频加入队列:', audioKey)
    // 如果正在播放，加入队列
    if (isPlayingAudio.value) {
      audioQueue.value.push({ audioKey, options })
      console.log('[AudioFeedback] 音频已加入队列，队列长度:', audioQueue.value.length)
      return true
    }
    // 如果没有正在播放，立即播放
    const result = await playAudioFile(audioKey, options)
    // 播放完成后处理队列
    if (audioQueue.value.length > 0) {
      setTimeout(processAudioQueue, 100)
    }
    return result
  }

  /**
   * 播放提示音
   * @param {string} type - 音效类型
   */
  const playSound = (type) => {
    if (!isEnabled.value) return

    const currentTime = Date.now()
    // 音效频率控制：避免过于频繁的提示音
    if (isPlayingSound.value || currentTime - lastSoundTime.value < soundCooldown) {
      console.log('[AudioFeedback] 音效被频率控制跳过:', type)
      return
    }
    initAudioContext()
    if (!audioContext.value) return

    const ctx = audioContext.value
    const oscillator = ctx.createOscillator()
    const gainNode = ctx.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(ctx.destination)

    // 更新音效播放状态
    lastSoundTime.value = currentTime
    isPlayingSound.value = true

    // 根据类型设置音效参数和持续时间
    let duration = 0.1 // 默认持续时间

    switch (type) {
      case 'success':
        // 成功音：上升音调
        oscillator.frequency.setValueAtTime(523.25, ctx.currentTime) // C5
        oscillator.frequency.exponentialRampToValueAtTime(783.99, ctx.currentTime + 0.3) // G5
        gainNode.gain.setValueAtTime(volume.value * 0.3, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3)
        duration = 0.3
        break

      case 'warning':
        // 警告音：短促的中音
        oscillator.frequency.setValueAtTime(440, ctx.currentTime) // A4
        gainNode.gain.setValueAtTime(volume.value * 0.2, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2)
        duration = 0.2
        break

      case 'error':
        // 错误音：下降音调
        oscillator.frequency.setValueAtTime(349.23, ctx.currentTime) // F4
        oscillator.frequency.exponentialRampToValueAtTime(196, ctx.currentTime + 0.4) // G3
        gainNode.gain.setValueAtTime(volume.value * 0.3, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.4)
        duration = 0.4
        break
      case 'start':
        // 开始音：简单的提示音
        oscillator.frequency.setValueAtTime(523.25, ctx.currentTime) // C5
        gainNode.gain.setValueAtTime(volume.value * 0.2, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.15)
        duration = 0.15
        break

      default:
        // 默认提示音
        oscillator.frequency.setValueAtTime(440, ctx.currentTime)
        gainNode.gain.setValueAtTime(volume.value * 0.1, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.1)
        duration = 0.1
    }

    // 播放音效
    oscillator.start(ctx.currentTime)
    oscillator.stop(ctx.currentTime + duration)

    // 音效结束后重置播放状态
    oscillator.onended = () => {
      isPlayingSound.value = false
    }

    // 备用重置机制（防止onended事件未触发）
    setTimeout(() => {
      isPlayingSound.value = false
    }, duration * 1000 + 100)
  }

  // 状态变化跟踪，用于控制语音触发
  const lastState = ref('')
  const stateChangeTime = ref(0)
  const lastErrorTime = ref(0) // 专门跟踪ERROR状态的时间

  /**
   * 根据训练状态提供音频反馈
   * @param {string} state - 训练状态
   * @param {number} score - 当前分数
   * @param {string} feedback - 反馈文本
   */
  const provideFeedback = (state, score, feedback) => {
    if (!isEnabled.value) return

    const currentTime = Date.now()
    const isStateChanged = state !== lastState.value
    // 更新状态跟踪
    if (isStateChanged) {
      lastState.value = state
      stateChangeTime.value = currentTime
      console.log('[AudioFeedback] 状态变化:', state)
    }
    // 只在状态变化时才提供反馈，避免重复触发
    if (!isStateChanged) {
      return
    }
    // 增加状态变化的最小间隔，避免过于频繁的状态切换
    const minStateInterval = 1000 // 1秒最小间隔
    if (currentTime - stateChangeTime.value < minStateInterval && lastState.value !== 'ERROR') {
      console.log('[AudioFeedback] 状态变化过于频繁，跳过反馈:', state)
      return
    }
    switch (state) {
      case 'COMPLETED':
        playSound('success')
        if (score >= 90) {
          speak('excellent')
        } else if (score >= 70) {
          speak('good')
        } else {
          speak('fair')
        }
        break
      case 'MOVING_TO_TARGET':
      case 'APPROACHING':
      case 'RAISING':
        // 不播放语音，避免干扰
        break

      case 'HOLDING':
      case 'TOUCHING':
        speak('hold')
        break

      case 'RETURNING':
      case 'LOWERING':
        speak('return')
        break

      case 'ERROR':
        // ERROR状态需要特殊处理，避免循环
        const timeSinceLastError = currentTime - (lastErrorTime.value || 0)
        if (timeSinceLastError > 3000) { // 3秒内只播放一次错误提示
          lastErrorTime.value = currentTime
          playSound('error')
          speak('error')
        } else {
          console.log('[AudioFeedback] ERROR状态过于频繁，跳过语音反馈')
        }
        break

      case 'IDLE':
        if (feedback && feedback.includes('准备')) {
          playSound('start')
          speak('prepare')
        }
        break
    }
  }

  /**
   * 播报动作指导
   * @param {string} actionType - 动作类型
   * @param {string} side - 侧别
   */
  const announceAction = (actionType, side) => {
    if (!isEnabled.value) return

    // 构建音频键名
    const audioKey = `${actionType}_${side}`
    console.log('[AudioFeedback] 播报动作指导:')
    console.log('  - actionType:', actionType)
    console.log('  - side:', side)
    console.log('  - 构建的音频键名:', audioKey)

    // 检查音频文件是否存在
    let audioPath = null
    for (const [categoryName, category] of Object.entries(AUDIO_FILES)) {
      if (category[audioKey]) {
        audioPath = category[audioKey]
        console.log('  - 找到音频文件:', categoryName, '->', audioPath)
        break
      }
    }

    if (!audioPath) {
      console.warn('[AudioFeedback] 未找到对应的音频文件:', audioKey)
      console.log('  - 可用的音频键名:', Object.keys(AUDIO_FILES.guidance))
    }

    speak(audioKey)
  }

  /**
   * 设置音量
   * @param {number} newVolume - 新音量 (0-1)
   */
  const setVolume = (newVolume) => {
    volume.value = Math.max(0, Math.min(1, newVolume))
  }

  /**
   * 切换音频开关
   */
  const toggleAudio = () => {
    isEnabled.value = !isEnabled.value
    if (isEnabled.value) {
      speak('audio_enabled')
    }
  }

  /**
   * 停止所有音频（清理状态但不强制中断）
   */
  const stopAll = () => {
    // 清理队列和状态
    audioQueue.value = []
    isProcessingQueue.value = false
    lastAudioTime.value = 0
    lastAudioKey.value = ''
    lastState.value = ''
    lastErrorTime.value = 0
    console.log('[AudioFeedback] 音频状态已清理')
  }

  return {
    // 状态
    isEnabled,
    volume,
    isPlayingAudio,
    isPlayingSound,
    isAudioContextActivated,

    // 方法
    speak,
    playAudioFile,
    playSound,
    provideFeedback,
    announceAction,
    setVolume,
    toggleAudio,
    stopAll,
    activateAudioContext,
    preloadAudioFiles
  }
}
