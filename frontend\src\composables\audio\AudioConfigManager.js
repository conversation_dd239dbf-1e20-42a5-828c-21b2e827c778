/**
 * 音频配置管理器
 * 负责音频文件路径配置、音频类型定义和优先级规则
 */

export class AudioConfigManager {
  constructor() {
    this.AUDIO_BASE_PATH = '/resources/audio'
    this.initializeAudioConfig()
    this.initializePriorityRules()
    this.initializeCooldownRules()
  }

  /**
   * 初始化音频文件配置
   */
  initializeAudioConfig() {
    this.AUDIO_FILES = {
      // 系统提示 - 最高优先级
      system: {
        audio_enabled: `${this.AUDIO_BASE_PATH}/system/audio_enabled.mp3`,
        session_start: `${this.AUDIO_BASE_PATH}/system/session_start.mp3`,
        session_pause: `${this.AUDIO_BASE_PATH}/system/session_pause.mp3`,
        session_resume: `${this.AUDIO_BASE_PATH}/system/session_resume.mp3`,
        session_end: `${this.AUDIO_BASE_PATH}/system/session_end.mp3`
      },

      // 动作指导 - 高优先级
      guidance: {
        shoulder_touch_left: `${this.AUDIO_BASE_PATH}/guidance/shoulder_touch/left_start.mp3`,
        shoulder_touch_right: `${this.AUDIO_BASE_PATH}/guidance/shoulder_touch/right_start.mp3`,
        arm_raise_left: `${this.AUDIO_BASE_PATH}/guidance/arm_raise/left_start.mp3`,
        arm_raise_right: `${this.AUDIO_BASE_PATH}/guidance/arm_raise/right_start.mp3`,
        finger_touch_left: `${this.AUDIO_BASE_PATH}/guidance/finger_touch/left_start.mp3`,
        finger_touch_right: `${this.AUDIO_BASE_PATH}/guidance/finger_touch/right_start.mp3`,
        palm_flip_left: `${this.AUDIO_BASE_PATH}/guidance/palm_flip/left_start.mp3`,
        palm_flip_right: `${this.AUDIO_BASE_PATH}/guidance/palm_flip/right_start.mp3`
      },

      // 详细指导 - 中高优先级
      detailed_guidance: {
        // 肩膀触摸详细指导
        shoulder_touch_closer: `${this.AUDIO_BASE_PATH}/detailed_guidance/shoulder_touch/closer.mp3`,
        shoulder_touch_left_closer: `${this.AUDIO_BASE_PATH}/detailed_guidance/shoulder_touch/left_closer.mp3`,
        shoulder_touch_right_closer: `${this.AUDIO_BASE_PATH}/detailed_guidance/shoulder_touch/right_closer.mp3`,
        
        // 手臂上举详细指导
        arm_raise_left_higher: `${this.AUDIO_BASE_PATH}/detailed_guidance/arm_raise/left_higher.mp3`,
        arm_raise_left_straight: `${this.AUDIO_BASE_PATH}/detailed_guidance/arm_raise/left_straight.mp3`,
        arm_raise_right_higher: `${this.AUDIO_BASE_PATH}/detailed_guidance/arm_raise/right_higher.mp3`,
        arm_raise_right_straight: `${this.AUDIO_BASE_PATH}/detailed_guidance/arm_raise/right_straight.mp3`,
        
        // 指尖对触详细指导
        finger_touch_closer: `${this.AUDIO_BASE_PATH}/detailed_guidance/finger_touch/closer.mp3`,
        
        // 手掌翻转详细指导
        palm_flip_left_range: `${this.AUDIO_BASE_PATH}/detailed_guidance/palm_flip/left_range.mp3`,
        palm_flip_right_range: `${this.AUDIO_BASE_PATH}/detailed_guidance/palm_flip/right_range.mp3`,
        
        // 通用指导
        frame_complete: `${this.AUDIO_BASE_PATH}/detailed_guidance/general/frame_complete.mp3`,
        stability: `${this.AUDIO_BASE_PATH}/detailed_guidance/general/stability.mp3`
      },

      // 状态反馈 - 中等优先级
      feedback: {
        // 状态提示
        hold: `${this.AUDIO_BASE_PATH}/feedback/states/hold.mp3`,
        return: `${this.AUDIO_BASE_PATH}/feedback/states/return.mp3`,
        error: `${this.AUDIO_BASE_PATH}/feedback/states/error.mp3`,
        prepare: `${this.AUDIO_BASE_PATH}/feedback/states/prepare.mp3`,

        // 完成度评价
        excellent: `${this.AUDIO_BASE_PATH}/feedback/completion/excellent.mp3`,
        good: `${this.AUDIO_BASE_PATH}/feedback/completion/good.mp3`,
        fair: `${this.AUDIO_BASE_PATH}/feedback/completion/fair.mp3`,

        // 鼓励语音
        keep_going: `${this.AUDIO_BASE_PATH}/feedback/encouragement/keep_going.mp3`,
        almost_there: `${this.AUDIO_BASE_PATH}/feedback/encouragement/almost_there.mp3`,
        good_progress: `${this.AUDIO_BASE_PATH}/feedback/encouragement/good_progress.mp3`
      }
    }
  }

  /**
   * 初始化优先级规则
   */
  initializePriorityRules() {
    this.PRIORITY_LEVELS = {
      CRITICAL: 1,    // 系统提示
      HIGH: 2,        // 动作指导
      MEDIUM_HIGH: 3, // 详细指导
      MEDIUM: 4,      // 状态反馈
      LOW: 5          // 鼓励语音
    }

    this.AUDIO_PRIORITIES = {
      system: this.PRIORITY_LEVELS.CRITICAL,
      guidance: this.PRIORITY_LEVELS.HIGH,
      detailed_guidance: this.PRIORITY_LEVELS.MEDIUM_HIGH,
      feedback: this.PRIORITY_LEVELS.MEDIUM,
      encouragement: this.PRIORITY_LEVELS.LOW
    }
  }

  /**
   * 初始化冷却时间规则
   */
  initializeCooldownRules() {
    this.COOLDOWN_RULES = {
      system: 0,           // 系统音频无冷却
      guidance: 0,         // 动作指导无冷却（每个动作只播放一次）
      detailed_guidance: 3000,  // 详细指导3秒冷却
      feedback: 1500,      // 状态反馈1.5秒冷却
      encouragement: 5000  // 鼓励语音5秒冷却
    }
  }

  /**
   * 获取音频文件路径
   * @param {string} audioKey - 音频键名
   * @returns {string|null} 音频文件路径
   */
  getAudioPath(audioKey) {
    for (const [category, files] of Object.entries(this.AUDIO_FILES)) {
      if (files[audioKey]) {
        return files[audioKey]
      }
    }
    return null
  }

  /**
   * 获取音频类别
   * @param {string} audioKey - 音频键名
   * @returns {string|null} 音频类别
   */
  getAudioCategory(audioKey) {
    for (const [category, files] of Object.entries(this.AUDIO_FILES)) {
      if (files[audioKey]) {
        return category
      }
    }
    return null
  }

  /**
   * 获取音频优先级
   * @param {string} audioKey - 音频键名
   * @returns {number} 优先级数值（越小优先级越高）
   */
  getAudioPriority(audioKey) {
    const category = this.getAudioCategory(audioKey)
    return this.AUDIO_PRIORITIES[category] || this.PRIORITY_LEVELS.LOW
  }

  /**
   * 获取音频冷却时间
   * @param {string} audioKey - 音频键名
   * @returns {number} 冷却时间（毫秒）
   */
  getAudioCooldown(audioKey) {
    const category = this.getAudioCategory(audioKey)
    return this.COOLDOWN_RULES[category] || 2000
  }

  /**
   * 检查音频是否存在
   * @param {string} audioKey - 音频键名
   * @returns {boolean} 是否存在
   */
  hasAudio(audioKey) {
    return this.getAudioPath(audioKey) !== null
  }

  /**
   * 获取所有可用的音频键名
   * @returns {Array<string>} 音频键名数组
   */
  getAllAudioKeys() {
    const keys = []
    for (const files of Object.values(this.AUDIO_FILES)) {
      keys.push(...Object.keys(files))
    }
    return keys
  }

  /**
   * 根据动作类型和侧别构建音频键名
   * @param {string} actionType - 动作类型
   * @param {string} side - 侧别
   * @returns {string} 音频键名
   */
  buildActionAudioKey(actionType, side) {
    return `${actionType}_${side}`
  }

  /**
   * 根据动作和错误类型构建详细指导音频键名
   * @param {string} actionType - 动作类型
   * @param {string} errorType - 错误类型
   * @param {string} side - 侧别（可选）
   * @returns {string} 音频键名
   */
  buildDetailedGuidanceKey(actionType, errorType, side = null) {
    if (side) {
      return `${actionType}_${side}_${errorType}`
    }
    return `${actionType}_${errorType}`
  }
}
