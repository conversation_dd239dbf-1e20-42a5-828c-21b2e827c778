/**
 * 优化音频系统测试
 * 验证新的模块化音频反馈系统功能
 */

import { useAudioFeedback } from '@/composables/useAudioFeedback'

export function runAudioSystemTest() {
  console.log('🧪 开始音频系统测试...')
  
  const audioFeedback = useAudioFeedback()
  
  // 测试1: 系统初始化
  console.log('📋 测试1: 系统初始化')
  const status = audioFeedback.getStatus()
  console.log('系统状态:', status)
  
  // 测试2: 预加载音频文件
  console.log('📋 测试2: 预加载音频文件')
  audioFeedback.preloadAudioFiles()
  
  // 测试3: 激活音频上下文
  console.log('📋 测试3: 激活音频上下文')
  audioFeedback.activateAudioContext().then(activated => {
    console.log('音频上下文激活结果:', activated)
  })
  
  // 测试4: 播放系统音频
  console.log('📋 测试4: 播放系统音频')
  setTimeout(() => {
    audioFeedback.speak('session_start')
  }, 1000)
  
  // 测试5: 播放动作指导音频
  console.log('📋 测试5: 播放动作指导音频')
  setTimeout(() => {
    audioFeedback.announceAction('shoulder_touch', 'left')
  }, 3000)
  
  // 测试6: 状态反馈测试
  console.log('📋 测试6: 状态反馈测试')
  setTimeout(() => {
    // 模拟状态变化序列
    audioFeedback.provideFeedback('IDLE', 0, '准备开始')
    
    setTimeout(() => {
      audioFeedback.provideFeedback('MOVING_TO_TARGET', 25, '正在移动')
    }, 2000)
    
    setTimeout(() => {
      audioFeedback.provideFeedback('HOLDING', 75, '保持动作')
    }, 4000)
    
    setTimeout(() => {
      audioFeedback.provideFeedback('RETURNING', 80, '返回初始位置')
    }, 6000)
    
    setTimeout(() => {
      audioFeedback.provideFeedback('COMPLETED', 85, '动作完成')
    }, 8000)
  }, 5000)
  
  // 测试7: 音效测试
  console.log('📋 测试7: 音效测试')
  setTimeout(() => {
    audioFeedback.playSound('start')
    
    setTimeout(() => {
      audioFeedback.playSound('success')
    }, 1000)
    
    setTimeout(() => {
      audioFeedback.playSound('warning')
    }, 2000)
    
    setTimeout(() => {
      audioFeedback.playSound('error')
    }, 3000)
  }, 15000)
  
  // 测试8: 优先级队列测试
  console.log('📋 测试8: 优先级队列测试')
  setTimeout(() => {
    // 快速连续播放多个音频，测试队列和优先级
    audioFeedback.speak('keep_going')  // 低优先级
    audioFeedback.speak('session_pause')  // 高优先级
    audioFeedback.speak('good_progress')  // 低优先级
    audioFeedback.speak('error')  // 中等优先级
  }, 20000)
  
  // 测试9: 冷却时间测试
  console.log('📋 测试9: 冷却时间测试')
  setTimeout(() => {
    // 快速重复播放同一音频，测试冷却机制
    audioFeedback.speak('hold')
    audioFeedback.speak('hold')  // 应该被冷却机制跳过
    audioFeedback.speak('hold')  // 应该被冷却机制跳过
  }, 25000)
  
  // 测试10: 动作切换测试
  console.log('📋 测试10: 动作切换测试')
  setTimeout(() => {
    // 设置第一个动作
    audioFeedback.setCurrentAction({
      action_type: 'shoulder_touch',
      side: 'left',
      action_id: 'test_action_1',
      action_name: '左侧触肩'
    })
    
    audioFeedback.announceAction('shoulder_touch', 'left')
    
    setTimeout(() => {
      // 清空播放记录并切换到新动作
      audioFeedback.clearCurrentActionPlayed()
      
      audioFeedback.setCurrentAction({
        action_type: 'arm_raise',
        side: 'right',
        action_id: 'test_action_2',
        action_name: '右臂上举'
      })
      
      audioFeedback.announceAction('arm_raise', 'right')
    }, 3000)
  }, 30000)
  
  console.log('🧪 音频系统测试已启动，请观察控制台输出和音频播放效果')
  console.log('📊 测试将在35秒内完成')
  
  // 最终状态报告
  setTimeout(() => {
    console.log('📊 最终系统状态:')
    console.log(audioFeedback.getStatus())
    console.log('✅ 音频系统测试完成')
  }, 35000)
}

// 导出测试函数供开发环境使用
if (import.meta.env.DEV) {
  window.runAudioSystemTest = runAudioSystemTest
  console.log('🔧 开发模式：可通过 window.runAudioSystemTest() 运行音频系统测试')
}
