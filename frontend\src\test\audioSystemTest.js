/**
 * 简化音频系统测试
 * 验证简化版音频反馈系统功能
 */

import { useAudioFeedback } from '@/composables/useAudioFeedback'

export function runSimpleAudioSystemTest() {
  console.log('🧪 开始简化音频系统测试...')

  const audioFeedback = useAudioFeedback()

  // 测试1: 系统初始化
  console.log('📋 测试1: 系统初始化')
  const status = audioFeedback.getStatus()
  console.log('系统状态:', status)

  // 测试2: 激活音频上下文
  console.log('📋 测试2: 激活音频上下文')
  audioFeedback.activateAudioContext().then(activated => {
    console.log('音频上下文激活结果:', activated)
  })

  // 测试3: 播放系统音频
  console.log('📋 测试3: 播放系统音频')
  setTimeout(() => {
    audioFeedback.speak('session_start')
  }, 1000)

  // 测试4: 播放动作指导音频（动作开始）
  console.log('📋 测试4: 播放动作指导音频')
  setTimeout(() => {
    audioFeedback.setCurrentAction({
      action_type: 'shoulder_touch',
      side: 'left',
      action_id: 'test_action_1',
      action_name: '左侧触肩'
    })
    audioFeedback.announceAction('shoulder_touch', 'left')
  }, 3000)

  // 测试5: 简化状态反馈测试（只有关键状态）
  console.log('📋 测试5: 简化状态反馈测试')
  setTimeout(() => {
    console.log('  - 测试HOLDING状态')
    audioFeedback.provideFeedback('HOLDING', 75, '保持动作')

    setTimeout(() => {
      console.log('  - 测试RETURNING状态')
      audioFeedback.provideFeedback('RETURNING', 80, '返回初始位置')
    }, 3000)

    setTimeout(() => {
      console.log('  - 测试COMPLETED状态')
      audioFeedback.provideFeedback('COMPLETED', 85, '动作完成')
    }, 6000)
  }, 6000)

  // 测试6: 音效测试
  console.log('📋 测试6: 音效测试')
  setTimeout(() => {
    console.log('  - 播放成功音效')
    audioFeedback.playSound('success')

    setTimeout(() => {
      console.log('  - 播放错误音效')
      audioFeedback.playSound('error')
    }, 2000)
  }, 13000)

  // 测试7: 动作切换测试
  console.log('📋 测试7: 动作切换测试')
  setTimeout(() => {
    console.log('  - 切换到右臂上举动作')
    // 清空播放记录并切换到新动作
    audioFeedback.clearCurrentActionPlayed()

    audioFeedback.setCurrentAction({
      action_type: 'arm_raise',
      side: 'right',
      action_id: 'test_action_2',
      action_name: '右臂上举'
    })

    audioFeedback.announceAction('arm_raise', 'right')

    // 测试新动作的状态反馈
    setTimeout(() => {
      console.log('  - 新动作的HOLDING状态')
      audioFeedback.provideFeedback('HOLDING', 70, '保持动作')
    }, 3000)

    setTimeout(() => {
      console.log('  - 新动作的COMPLETED状态')
      audioFeedback.provideFeedback('COMPLETED', 92, '动作完成')
    }, 6000)
  }, 18000)

  // 测试8: 会话结束
  console.log('📋 测试8: 会话结束')
  setTimeout(() => {
    console.log('  - 播放会话结束音频')
    audioFeedback.speak('session_end')
  }, 28000)

  console.log('🧪 简化音频系统测试已启动')
  console.log('📊 测试特点：')
  console.log('  - 每个动作只有3-4个关键音频节点')
  console.log('  - 动作开始：指导音频')
  console.log('  - 执行过程：保持、返回提示')
  console.log('  - 动作完成：评价音频')
  console.log('📊 测试将在30秒内完成')

  // 最终状态报告
  setTimeout(() => {
    console.log('📊 最终系统状态:')
    console.log(audioFeedback.getStatus())
    console.log('✅ 简化音频系统测试完成')
    console.log('🎯 简化效果：')
    console.log('  - 减少了复杂的详细指导音频')
    console.log('  - 移除了频繁的鼓励语音')
    console.log('  - 保留了核心的动作生命周期音频')
  }, 30000)
}

// 导出测试函数供开发环境使用
if (import.meta.env.DEV) {
  window.runSimpleAudioSystemTest = runSimpleAudioSystemTest
  console.log('🔧 开发模式：可通过 window.runSimpleAudioSystemTest() 运行简化音频系统测试')
}
