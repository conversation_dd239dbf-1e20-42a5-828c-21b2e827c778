/**
 * 训练动作和进度管理
 * 负责管理康复动作列表、当前动作和训练进度
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { formatTrainingData, saveToLocalStorage, downloadAsJSON } from '@/utils/trainingDataManager'

export const useTrainingStore = defineStore('training', () => {
  // 训练相关数据
  const currentAction = ref(null)
  const actionList = ref([])
  const reportData = ref(null)
  const trainingSession = ref(null)

  // 默认康复动作数据
  const defaultActions = [
    {
      action_id: 1,
      action_type: "shoulder_touch",
      action_name: "对侧触肩",
      side: "left",
      video_url: "/resources/video/1.mp4",
      description:
        "身体站直，用一只手触摸对侧的肩膀，然后交替进行，保持动作的稳定与流畅。",
      goal: "增强肩部关节的灵活性和身体的协调能力。",
      score: 0,
      difficulty_level: "easy",
      status: "pending", // pending, active, completed
    },
    {
      action_id: 2,
      action_type: "arm_raise",
      action_name: "手臂上举",
      side: "left",
      video_url: "/resources/video/2.mp4",
      description:
        "保持躯干稳定，缓慢将手臂从体侧向上举起至最高点，感受肩部的完全伸展。",
      goal: "提升肩关节的活动范围，强化上肢核心肌群。",
      score: 0,
      difficulty_level: "easy",
      status: "pending",
    },
    {
      action_id: 3,
      action_type: "finger_touch",
      action_name: "指尖对触",
      side: "left",
      difficulty_level: "easy",
      video_url: "/resources/video/3.mp4",
      description:
        "将拇指指尖依次与其他四指的指尖进行轻轻触碰，动作要清晰、有节奏。",
      goal: "提高手指的精细运动能力、灵活性和协调性。",
      score: 0,
      status: "pending",
    },
    {
      action_id: 4,
      action_type: "palm_flip",
      action_name: "手掌翻转",
      difficulty_level: "easy",
      side: "left",
      video_url: "/resources/video/4.mp4",
      description:
        "将前臂放置于平面上，快速、有节奏地进行手心向上和手心向下的翻转动作。",
      goal: "锻炼前臂旋转肌群，提升手腕的快速协调能力。",
      score: 0,
      status: "pending",
    },
  ];

  // 计算属性
  const currentActionName = computed(() => {
    return currentAction.value ? currentAction.value.action_type : null
  })

  const currentActionIndex = computed(() => {
    if (!currentAction.value || !actionList.value.length) return -1
    return actionList.value.findIndex(action => action.action_id === currentAction.value.action_id)
  })

  const completedActionsCount = computed(() => {
    return actionList.value.filter(action => action.status === 'completed').length
  })

  const isAllActionsCompleted = computed(() => {
    return actionList.value.length > 0 && completedActionsCount.value === actionList.value.length
  })

  /**
   * 初始化动作列表
   */
  const initializeActions = () => {
    // 深拷贝默认动作数据
    actionList.value = JSON.parse(JSON.stringify(defaultActions))
    
    // 设置第一个动作为当前动作
    if (actionList.value.length > 0) {
      currentAction.value = actionList.value[0]
      actionList.value[0].status = 'active'
    }
    
    console.log('[Training] 动作列表初始化完成')
  }
  /**
   * 切换到下一个动作
   */
  const moveToNextAction = () => {
    const currentIndex = currentActionIndex.value
    
    if (currentIndex === -1 || currentIndex >= actionList.value.length - 1) {
      console.log('[Training] 已是最后一个动作或无有效动作')
      return false
    }

    // 标记当前动作为完成
    if (currentAction.value) {
      const currentActionInList = actionList.value.find(action => action.action_id === currentAction.value.action_id)
      if (currentActionInList) {
        currentActionInList.status = 'completed'
      }
    }

    // 切换到下一个动作
    const nextAction = actionList.value[currentIndex + 1]
    currentAction.value = nextAction
    nextAction.status = 'active'
    
    console.log(`[Training] 切换到下一个动作: ${nextAction.action_name}`)
    return true
  }

  /**
   * 完成当前动作
   */
  const completeCurrentAction = (score = 0) => {
    if (!currentAction.value) return false

    // 更新动作得分
    currentAction.value.score = score
    
    // 在动作列表中找到对应动作并更新
    const actionInList = actionList.value.find(action => action.action_id === currentAction.value.action_id)
    if (actionInList) {
      actionInList.score = score
      actionInList.status = 'completed'
    }

    console.log(`[Training] 动作完成: ${currentAction.value.action_name}, 得分: ${score}`)
    return true
  }


  return {
    // 响应式数据
    currentAction,
    actionList,
    reportData,
    trainingSession,

    // 计算属性
    currentActionName,
    currentActionIndex,
    completedActionsCount,
    isAllActionsCompleted,

    // 方法
    initializeActions,
    moveToNextAction,
    completeCurrentAction,
  }
})
