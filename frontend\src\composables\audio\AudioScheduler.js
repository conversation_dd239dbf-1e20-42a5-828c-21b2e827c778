/**
 * 音频调度器
 * 核心调度逻辑，协调各个模块，管理音频播放流程
 */

export class AudioScheduler {
  constructor({ configManager, priorityQueue, stateManager, isEnabled, volume }) {
    this.configManager = configManager
    this.priorityQueue = priorityQueue
    this.stateManager = stateManager
    this.isEnabled = isEnabled
    this.volume = volume
    
    this.audioContext = null
    this.audioCache = new Map()
    this.isAudioContextActivated = false
    this.isProcessingQueue = false
    
    this.initializeAudioContext()
  }

  /**
   * 初始化音频上下文
   */
  initializeAudioContext() {
    if (!this.audioContext && window.AudioContext) {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
    }
  }

  /**
   * 激活音频上下文（需要用户交互）
   */
  async activateAudioContext() {
    if (this.isAudioContextActivated) return true
    
    try {
      this.initializeAudioContext()
      if (this.audioContext && this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }
      this.isAudioContextActivated = true
      console.log('[AudioScheduler] 音频上下文已激活')
      return true
    } catch (error) {
      console.warn('[AudioScheduler] 音频上下文激活失败:', error)
      return false
    }
  }

  /**
   * 获取或创建音频元素
   */
  getAudioElement(audioPath) {
    if (this.audioCache.has(audioPath)) {
      return this.audioCache.get(audioPath)
    }
    const audio = new Audio(audioPath)
    audio.volume = this.volume.value
    audio.preload = 'auto'
    this.audioCache.set(audioPath, audio)
    return audio
  }

  /**
   * 播放音频文件
   * @param {string} audioKey - 音频键名
   * @param {Object} options - 播放选项
   */
  async playAudioFile(audioKey, options = {}) {
    if (!this.isEnabled.value) {
      console.log('[AudioScheduler] 音频播放被禁用')
      return false
    }
    // 激活音频上下文
    await this.activateAudioContext()
    // 获取音频路径
    const audioPath = this.configManager.getAudioPath(audioKey)
    if (!audioPath) {
      console.warn('[AudioScheduler] 未找到音频文件:', audioKey)
      return false
    }
    try {
      console.log('[AudioScheduler] 播放音频文件:', audioKey, audioPath)

      const audio = this.getAudioElement(audioPath)
      audio.volume = this.volume.value * (options.volume || 1.0)

      // 设置当前播放状态
      this.priorityQueue.setCurrentAudio(audioKey)

      // 播放音频
      await audio.play()

      // 监听播放结束
      return new Promise((resolve) => {
        const handleEnded = () => {
          console.log('[AudioScheduler] 音频播放完成:', audioKey)
          this.priorityQueue.clearCurrentAudio()
          this.priorityQueue.markAsPlayed(audioKey)
          audio.removeEventListener('ended', handleEnded)
          audio.removeEventListener('error', handleError)
          resolve(true)
        }
        const handleError = (error) => {
          console.warn('[AudioScheduler] 音频播放出错:', audioKey, error)
          this.priorityQueue.clearCurrentAudio()
          audio.removeEventListener('ended', handleEnded)
          audio.removeEventListener('error', handleError)
          resolve(false)
        }
        audio.addEventListener('ended', handleEnded)
        audio.addEventListener('error', handleError)
      })

    } catch (error) {
      console.warn('[AudioScheduler] 音频播放失败:', audioKey, error)
      this.priorityQueue.clearCurrentAudio()
      return false
    }
  }

  /**
   * 播放音效
   * @param {string} type - 音效类型
   */
  playSound(type) {
    if (!this.isEnabled.value) return

    this.initializeAudioContext()
    if (!this.audioContext) return

    const ctx = this.audioContext
    const oscillator = ctx.createOscillator()
    const gainNode = ctx.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(ctx.destination)

    // 根据类型设置音效参数
    let duration = 0.1
    switch (type) {
      case 'success':
        oscillator.frequency.setValueAtTime(523.25, ctx.currentTime)
        oscillator.frequency.exponentialRampToValueAtTime(783.99, ctx.currentTime + 0.3)
        gainNode.gain.setValueAtTime(this.volume.value * 0.3, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3)
        duration = 0.3
        break

      case 'warning':
        oscillator.frequency.setValueAtTime(440, ctx.currentTime)
        gainNode.gain.setValueAtTime(this.volume.value * 0.2, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2)
        duration = 0.2
        break

      case 'error':
        oscillator.frequency.setValueAtTime(349.23, ctx.currentTime)
        oscillator.frequency.exponentialRampToValueAtTime(196, ctx.currentTime + 0.4)
        gainNode.gain.setValueAtTime(this.volume.value * 0.3, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.4)
        duration = 0.4
        break

      case 'start':
        oscillator.frequency.setValueAtTime(523.25, ctx.currentTime)
        gainNode.gain.setValueAtTime(this.volume.value * 0.2, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.15)
        duration = 0.15
        break

      default:
        oscillator.frequency.setValueAtTime(440, ctx.currentTime)
        gainNode.gain.setValueAtTime(this.volume.value * 0.1, ctx.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.1)
        duration = 0.1
    }

    oscillator.start(ctx.currentTime)
    oscillator.stop(ctx.currentTime + duration)
  }

  /**
   * 处理音频队列
   */
  async processQueue() {
    if (this.isProcessingQueue || this.priorityQueue.getQueueLength() === 0) {
      return
    }

    this.isProcessingQueue = true
    this.priorityQueue.setProcessing(true)

    while (this.priorityQueue.getQueueLength() > 0) {
      const queueItem = this.priorityQueue.dequeue()
      if (!queueItem) break

      console.log('[AudioScheduler] 处理队列中的音频:', queueItem.audioKey)

      await this.playAudioFile(queueItem.audioKey, queueItem.options)

      // 等待一小段时间再处理下一个
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    this.isProcessingQueue = false
    this.priorityQueue.setProcessing(false)
  }

  /**
   * 调度音频播放
   * @param {string} audioKey - 音频键名
   * @param {Object} options - 播放选项
   */
  async scheduleAudio(audioKey, options = {}) {
    if (!this.isEnabled.value) {
      console.log('[AudioScheduler] 音频播放被禁用')
      return false
    }
    // 获取音频配置信息
    const priority = this.configManager.getAudioPriority(audioKey)
    const cooldown = this.configManager.getAudioCooldown(audioKey)
    // 创建音频项目
    const audioItem = {
      audioKey,
      priority,
      cooldown,
      options
    }
    // 尝试加入队列
    const enqueued = this.priorityQueue.enqueue(audioItem)
    if (!enqueued) {
      return false
    }
    // 如果当前没有音频在播放，立即开始处理队列
    if (!this.priorityQueue.isPlaying()) {
      setTimeout(() => this.processQueue(), 100)
    }
    return true
  }

  /**
   * 处理训练状态反馈
   * @param {string} state - 训练状态
   * @param {number} score - 当前分数
   * @param {string} feedback - 反馈文本
   */
  async provideFeedback(state, score, feedback) {
    if (!this.isEnabled.value) return

    // 更新状态管理器并获取音频触发结果
    const triggers = this.stateManager.updateState(state, score, feedback)

    // 处理音效
    if (triggers.sound) {
      this.playSound(triggers.sound)
    }

    // 处理主要音频
    if (triggers.primary) {
      await this.scheduleAudio(triggers.primary.audioKey, { 
        reason: triggers.primary.reason 
      })
    }
    // 处理详细指导音频
    if (triggers.detailed) {
      await this.scheduleAudio(triggers.detailed.audioKey, { 
        reason: triggers.detailed.reason 
      })
    }

    // 处理鼓励音频
    if (triggers.encouragement) {
      await this.scheduleAudio(triggers.encouragement.audioKey, { 
        reason: triggers.encouragement.reason 
      })
    }
  }

  /**
   * 播报动作指导
   * @param {string} actionType - 动作类型
   * @param {string} side - 侧别
   */
  async announceAction(actionType, side) {
    if (!this.isEnabled.value) return

    // 构建音频键名
    const audioKey = this.configManager.buildActionAudioKey(actionType, side)

    // 检查音频文件是否存在
    if (!this.configManager.hasAudio(audioKey)) {
      console.warn('[AudioScheduler] 未找到对应的音频文件:', audioKey)
      return false
    }
    // 设置当前动作信息到状态管理器
    this.stateManager.setCurrentAction({
      action_type: actionType,
      side: side,
      action_id: `${actionType}_${side}_${Date.now()}`,
      action_name: `${actionType} ${side}`
    })

    // 清空当前动作的播放记录
    this.priorityQueue.clearCurrentActionPlayed()
    // 调度播放
    return await this.scheduleAudio(audioKey, { 
      reason: '动作指导',
      volume: 1.0 
    })
  }

  /**
   * 停止所有音频
   */
  stopAll() {
    this.priorityQueue.clear()
    this.stateManager.reset()
    this.isProcessingQueue = false
    console.log('[AudioScheduler] 所有音频已停止')
  }

  /**
   * 预加载音频文件
   */
  preloadAudioFiles() {
    console.log('[AudioScheduler] 开始预加载音频文件')

    const allKeys = this.configManager.getAllAudioKeys()
    let loadedCount = 0

    allKeys.forEach(key => {
      const path = this.configManager.getAudioPath(key)
      if (path) {
        this.getAudioElement(path)
        loadedCount++
      }
    })

    console.log(`[AudioScheduler] 音频文件预加载完成，缓存数量: ${loadedCount}`)
  }

  /**
   * 获取调度器状态
   */
  getStatus() {
    return {
      isEnabled: this.isEnabled.value,
      volume: this.volume.value,
      isAudioContextActivated: this.isAudioContextActivated,
      isProcessingQueue: this.isProcessingQueue,
      queueStatus: this.priorityQueue.getStatus(),
      stateStats: this.stateManager.getStats(),
      cacheSize: this.audioCache.size
    }
  }
}
